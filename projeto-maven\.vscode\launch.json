{"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug Current File", "request": "launch", "mainClass": "${file}", "projectName": "windchill-erp-integration", "console": "integratedTerminal", "args": [], "vmArgs": ["-Dfile.encoding=UTF-8", "-Djava.awt.headless=true"]}, {"type": "java", "name": "Debug Tests", "request": "launch", "mainClass": "", "projectName": "windchill-erp-integration", "console": "integratedTerminal", "preLaunchTask": "Maven: Test", "vmArgs": ["-Dfile.encoding=UTF-8", "-Djava.awt.headless=true"]}, {"type": "java", "name": "Debug ERP Integration Strategy", "request": "launch", "mainClass": "com.infoaxis.integracao.core.service.ERPIntegrationStrategy", "projectName": "windchill-erp-integration", "console": "integratedTerminal", "args": [], "vmArgs": ["-Dfile.encoding=UTF-8", "-Dwindchill.home=D:/Windchill", "-Djava.awt.headless=true"], "env": {"WINDCHILL_HOME": "D:/Windchill"}}, {"type": "java", "name": "Debug Datasul Integration", "request": "launch", "mainClass": "com.infoaxis.integracao.erp.datasul.DatasulIntegrationStrategy", "projectName": "windchill-erp-integration", "console": "integratedTerminal", "args": [], "vmArgs": ["-Dfile.encoding=UTF-8", "-Dwindchill.home=D:/Windchill", "-Djava.awt.headless=true", "-Ddatasul.debug=true"], "env": {"WINDCHILL_HOME": "D:/Windchill", "ERP_TYPE": "DATASUL"}}, {"type": "java", "name": "Debug <PERSON><PERSON><PERSON>", "request": "launch", "mainClass": "com.infoaxis.integracao.core.util.ErrorHandler", "projectName": "windchill-erp-integration", "console": "integratedTerminal", "args": [], "vmArgs": ["-Dfile.encoding=UTF-8", "-Djava.awt.headless=true", "-Dlogging.level.com.infoaxis=DEBUG"]}, {"type": "java", "name": "Attach to Windchill", "request": "attach", "hostName": "localhost", "port": 8787, "projectName": "windchill-erp-integration", "timeout": 30000}]}
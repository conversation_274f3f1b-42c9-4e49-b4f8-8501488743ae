package com.infoaxis.integracao.core.util;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.core.exception.ConnectionException;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.net.SocketTimeoutException;
import java.net.ConnectException;
import java.sql.SQLException;

/**
 * Utilitário centralizado para tratamento de erros de integração ERP.
 * Implementa estratégias defensivas e logging estruturado.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class ErrorHandler {
    
    private static final Logger LOGGER = Logger.getLogger(ErrorHandler.class.getName());
    
    // Códigos de erro padronizados
    public static final String CONNECTION_TIMEOUT = "ERR_CONN_TIMEOUT";
    public static final String CONNECTION_REFUSED = "ERR_CONN_REFUSED";
    public static final String DATABASE_ERROR = "ERR_DATABASE";
    public static final String AUTHENTICATION_ERROR = "ERR_AUTH";
    public static final String VALIDATION_ERROR = "ERR_VALIDATION";
    public static final String BUSINESS_RULE_ERROR = "ERR_BUSINESS";
    public static final String UNKNOWN_ERROR = "ERR_UNKNOWN";
    
    private ErrorHandler() {
        // Classe utilitária - construtor privado
    }
    
    /**
     * Trata erro genérico de integração com estratégia defensiva.
     * Versão melhorada do método trataErroGenerico original.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção capturada
     * @return Mensagem de erro tratada e formatada
     */
    public static String handleIntegrationError(String endPoint, Exception exception) {
        if (endPoint == null || endPoint.trim().isEmpty()) {
            endPoint = "ENDPOINT_DESCONHECIDO";
        }
        
        if (exception == null) {
            LOGGER.warning("Método handleIntegrationError chamado com exceção nula para endpoint: " + endPoint);
            return formatErrorMessage(UNKNOWN_ERROR, endPoint, "Erro desconhecido", null);
        }
        
        // Log do erro original para auditoria
        LOGGER.log(Level.SEVERE, "Erro de integração no endpoint: " + endPoint, exception);
        
        try {
            return categorizeAndFormatError(endPoint, exception);
        } catch (Exception handlingException) {
            // Fallback em caso de erro no próprio tratamento
            LOGGER.log(Level.SEVERE, "Erro no tratamento de exceção", handlingException);
            return formatErrorMessage(UNKNOWN_ERROR, endPoint, 
                "Erro interno no tratamento de exceções", handlingException);
        }
    }
    
    /**
     * Categoriza e formata o erro baseado no tipo de exceção.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção a ser categorizada
     * @return Mensagem formatada
     */
    private static String categorizeAndFormatError(String endPoint, Exception exception) {
        String errorCode;
        String userMessage;
        
        // Categorização baseada no tipo de exceção
        if (exception instanceof SocketTimeoutException) {
            errorCode = CONNECTION_TIMEOUT;
            userMessage = "Timeout na conexão com o sistema ERP";
        } else if (exception instanceof ConnectException) {
            errorCode = CONNECTION_REFUSED;
            userMessage = "Conexão recusada pelo sistema ERP";
        } else if (exception instanceof SQLException) {
            errorCode = DATABASE_ERROR;
            userMessage = "Erro de acesso ao banco de dados";
        } else if (exception instanceof SecurityException) {
            errorCode = AUTHENTICATION_ERROR;
            userMessage = "Erro de autenticação/autorização";
        } else if (exception instanceof IllegalArgumentException) {
            errorCode = VALIDATION_ERROR;
            userMessage = "Dados inválidos fornecidos";
        } else if (exception instanceof ERPIntegrationException) {
            // Exceções já tratadas pela nossa hierarquia
            ERPIntegrationException erpEx = (ERPIntegrationException) exception;
            return erpEx.getDetailedMessage();
        } else {
            errorCode = UNKNOWN_ERROR;
            userMessage = "Erro inesperado na integração";
        }
        
        return formatErrorMessage(errorCode, endPoint, userMessage, exception);
    }
    
    /**
     * Formata mensagem de erro de forma consistente.
     * 
     * @param errorCode Código do erro
     * @param endPoint Endpoint onde ocorreu
     * @param userMessage Mensagem amigável ao usuário
     * @param exception Exceção original (pode ser null)
     * @return Mensagem formatada
     */
    private static String formatErrorMessage(String errorCode, String endPoint, 
                                           String userMessage, Exception exception) {
        StringBuilder message = new StringBuilder();
        message.append("[").append(errorCode).append("] ");
        message.append("Endpoint: ").append(endPoint).append(" - ");
        message.append(userMessage);
        
        if (exception != null && exception.getMessage() != null) {
            message.append(" (Detalhes: ").append(exception.getMessage()).append(")");
        }
        
        return message.toString();
    }
    
    /**
     * Cria exceção de conexão específica baseada na exceção original.
     * 
     * @param endPoint Endpoint que falhou
     * @param serverUrl URL do servidor
     * @param timeoutSeconds Timeout configurado
     * @param cause Causa original
     * @return ConnectionException configurada
     */
    public static ConnectionException createConnectionException(String endPoint, String serverUrl, 
                                                              int timeoutSeconds, Throwable cause) {
        return new ConnectionException(endPoint, serverUrl, timeoutSeconds, cause);
    }
    
    /**
     * Verifica se uma exceção é recuperável (pode ser tentada novamente).
     * 
     * @param exception Exceção a ser verificada
     * @return true se for recuperável
     */
    public static boolean isRecoverableError(Exception exception) {
        return exception instanceof SocketTimeoutException ||
               exception instanceof ConnectException ||
               (exception instanceof SQLException && 
                exception.getMessage() != null && 
                exception.getMessage().contains("timeout"));
    }
}

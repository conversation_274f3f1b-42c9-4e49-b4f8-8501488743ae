/** Testes unitários para DatasulRetornoAdapter.
 * Valida todas as funcionalidades do adapter específico do Datasul.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | Fábio <PERSON>  | Testes unitários do adapter Datasul.
 */
package com.infoaxis.integracaoDatasul.adapters;

import com.infoaxis.core.RetornoAPI;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Testes unitários para DatasulRetornoAdapter.
 * 
 * Cobre todos os cenários de uso do adapter, incluindo:
 * - Criação e validação
 * - Formatação específica do Datasul
 * - Detecção de sucesso/erro
 * - Métodos utilitários
 * - Integração com código legado
 */
public class DatasulRetornoAdapterTest {
    
    private RetornoAPI retornoSucesso;
    private RetornoAPI retornoErroGenerico;
    private RetornoAPI retornoErroValidacao;
    private RetornoAPI retornoErroConexao;
    
    @BeforeEach
    void setUp() {
        retornoSucesso = new RetornoAPI(0, "Operação realizada com sucesso");
        retornoErroGenerico = new RetornoAPI(-1, "Erro genérico");
        retornoErroValidacao = new RetornoAPI(-2, "Erro de validação");
        retornoErroConexao = new RetornoAPI(-3, "Erro de conexão");
    }
    
    @Test
    @DisplayName("Deve criar adapter com RetornoAPI válido")
    void deveCriarAdapterComRetornoValido() {
        // When
        DatasulRetornoAdapter adapter = new DatasulRetornoAdapter(retornoSucesso);
        
        // Then
        assertNotNull(adapter);
        assertEquals(retornoSucesso, adapter.getRetornoOriginal());
    }
    
    @Test
    @DisplayName("Deve lançar exceção ao criar adapter com RetornoAPI null")
    void deveLancarExcecaoComRetornoNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new DatasulRetornoAdapter(null)
        );
        
        assertEquals("RetornoAPI não pode ser null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Deve detectar sucesso corretamente")
    void deveDetectarSucessoCorretamente() {
        // Given
        DatasulRetornoAdapter adapterSucesso = new DatasulRetornoAdapter(retornoSucesso);
        DatasulRetornoAdapter adapterErro = new DatasulRetornoAdapter(retornoErroGenerico);
        
        // When & Then
        assertTrue(adapterSucesso.isSuccessoDatasul());
        assertFalse(adapterSucesso.isErroDatasul());
        
        assertFalse(adapterErro.isSuccessoDatasul());
        assertTrue(adapterErro.isErroDatasul());
    }
    
    @Test
    @DisplayName("Deve identificar tipos de erro corretamente")
    void deveIdentificarTiposDeErroCorretamente() {
        // Given
        DatasulRetornoAdapter adapterSucesso = new DatasulRetornoAdapter(retornoSucesso);
        DatasulRetornoAdapter adapterErroGenerico = new DatasulRetornoAdapter(retornoErroGenerico);
        DatasulRetornoAdapter adapterErroValidacao = new DatasulRetornoAdapter(retornoErroValidacao);
        DatasulRetornoAdapter adapterErroConexao = new DatasulRetornoAdapter(retornoErroConexao);
        
        // When & Then
        assertEquals(DatasulRetornoAdapter.TipoErroDatasul.NENHUM, adapterSucesso.getTipoErro());
        assertEquals(DatasulRetornoAdapter.TipoErroDatasul.GENERICO, adapterErroGenerico.getTipoErro());
        assertEquals(DatasulRetornoAdapter.TipoErroDatasul.VALIDACAO, adapterErroValidacao.getTipoErro());
        assertEquals(DatasulRetornoAdapter.TipoErroDatasul.CONEXAO, adapterErroConexao.getTipoErro());
    }
    
    @Test
    @DisplayName("Deve formatar código corretamente")
    void deveFormatarCodigoCorretamente() {
        // Given
        DatasulRetornoAdapter adapter = new DatasulRetornoAdapter(retornoSucesso);
        
        // When
        String codigoFormatado = adapter.getCodigoFormatado();
        
        // Then
        assertNotNull(codigoFormatado);
        // Assumindo que UtilDatasul.formataCodigoDatasul existe e funciona
        // O teste real dependeria da implementação específica
    }
    
    @Test
    @DisplayName("Deve gerar mensagem formatada para log")
    void deveGerarMensagemFormatadaParaLog() {
        // Given
        DatasulRetornoAdapter adapterSucesso = new DatasulRetornoAdapter(retornoSucesso);
        DatasulRetornoAdapter adapterErro = new DatasulRetornoAdapter(retornoErroValidacao);
        
        // When
        String logSucesso = adapterSucesso.getMensagemFormatadaParaLog();
        String logErro = adapterErro.getMensagemFormatadaParaLog();
        
        // Then
        assertTrue(logSucesso.contains("DATASUL-SUCESSO"));
        assertTrue(logSucesso.contains("Nenhum erro"));
        assertTrue(logSucesso.contains("Operação realizada com sucesso"));
        
        assertTrue(logErro.contains("DATASUL-ERRO"));
        assertTrue(logErro.contains("Erro de validação"));
        assertTrue(logErro.contains("Erro de validação"));
    }
    
    @Test
    @DisplayName("Deve determinar prioridade de erro corretamente")
    void deveDeterminarPrioridadeErroCorretamente() {
        // Given
        DatasulRetornoAdapter adapterSucesso = new DatasulRetornoAdapter(retornoSucesso);
        DatasulRetornoAdapter adapterErroConexao = new DatasulRetornoAdapter(retornoErroConexao);
        DatasulRetornoAdapter adapterErroValidacao = new DatasulRetornoAdapter(retornoErroValidacao);
        DatasulRetornoAdapter adapterErroGenerico = new DatasulRetornoAdapter(retornoErroGenerico);
        
        // When & Then
        assertEquals(1, adapterSucesso.getPrioridadeErro()); // Baixa
        assertEquals(5, adapterErroConexao.getPrioridadeErro()); // Crítica
        assertEquals(3, adapterErroValidacao.getPrioridadeErro()); // Média
        assertEquals(4, adapterErroGenerico.getPrioridadeErro()); // Alta
    }
    
    @Test
    @DisplayName("Deve determinar necessidade de retry corretamente")
    void deveDeterminarNecessidadeRetryCorretamente() {
        // Given
        DatasulRetornoAdapter adapterSucesso = new DatasulRetornoAdapter(retornoSucesso);
        DatasulRetornoAdapter adapterErroConexao = new DatasulRetornoAdapter(retornoErroConexao);
        DatasulRetornoAdapter adapterErroValidacao = new DatasulRetornoAdapter(retornoErroValidacao);
        
        // When & Then
        assertFalse(adapterSucesso.deveReitentar());
        assertTrue(adapterErroConexao.deveReitentar()); // Erro de conexão deve retry
        assertFalse(adapterErroValidacao.deveReitentar()); // Erro de validação não deve retry
    }
    
    @Test
    @DisplayName("Deve converter para formato legado corretamente")
    void deveConverterParaFormatoLegadoCorretamente() {
        // Given
        DatasulRetornoAdapter adapter = new DatasulRetornoAdapter(retornoSucesso);
        
        // When
        String[] formatoLegado = adapter.toFormatoLegadoDatasul();
        
        // Then
        assertNotNull(formatoLegado);
        assertEquals(2, formatoLegado.length);
        assertNotNull(formatoLegado[0]); // Código formatado
        assertTrue(formatoLegado[1].contains("[DATASUL]")); // Mensagem com prefixo
        assertTrue(formatoLegado[1].contains("Operação realizada com sucesso"));
    }
    
    @Test
    @DisplayName("Deve criar adapter usando método factory 'of'")
    void deveCriarAdapterUsandoMetodoFactoryOf() {
        // When
        DatasulRetornoAdapter adapter = DatasulRetornoAdapter.of(retornoSucesso);
        DatasulRetornoAdapter adapterNull = DatasulRetornoAdapter.of(null);
        
        // Then
        assertNotNull(adapter);
        assertEquals(retornoSucesso, adapter.getRetornoOriginal());
        assertNull(adapterNull);
    }
    
    @Test
    @DisplayName("Deve criar adapter validado usando 'ofValidated'")
    void deveCriarAdapterValidadoUsandoOfValidated() {
        // When
        DatasulRetornoAdapter adapter = DatasulRetornoAdapter.ofValidated(retornoSucesso);
        
        // Then
        assertNotNull(adapter);
        assertEquals(retornoSucesso, adapter.getRetornoOriginal());
    }
    
    @Test
    @DisplayName("Deve lançar exceção em 'ofValidated' com RetornoAPI null")
    void deveLancarExcecaoEmOfValidatedComRetornoNull() {
        // When & Then
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            () -> DatasulRetornoAdapter.ofValidated(null)
        );
        
        assertTrue(exception.getMessage().contains("RetornoAPI é obrigatório"));
    }
}

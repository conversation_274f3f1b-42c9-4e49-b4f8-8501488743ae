@echo off
REM ============================================================================
REM Script de Build para Windchill ERP Integration
REM ============================================================================

echo.
echo ========================================
echo  Windchill ERP Integration - Build
echo ========================================
echo.

REM Verificar se Maven está instalado
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Maven não encontrado no PATH!
    echo Instale o Maven e adicione ao PATH do sistema.
    pause
    exit /b 1
)

REM Verificar se o diretório do Windchill existe
if not exist "D:\Windchill" (
    echo ERRO: Diretório do Windchill não encontrado em D:\Windchill
    echo Verifique se o Windchill está instalado corretamente.
    pause
    exit /b 1
)

REM Navegar para o diretório do projeto
cd /d "%~dp0.."

echo Iniciando build do projeto...
echo.

REM Executar clean e compile
echo [1/3] Limpando projeto anterior...
call mvn clean
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha na limpeza do projeto!
    pause
    exit /b 1
)

echo.
echo [2/3] Compilando código fonte...
call mvn compile
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha na compilação!
    pause
    exit /b 1
)

echo.
echo [3/3] Executando testes...
call mvn test
if %ERRORLEVEL% NEQ 0 (
    echo AVISO: Alguns testes falharam!
    echo Deseja continuar mesmo assim? (S/N)
    set /p continue=
    if /i not "%continue%"=="S" (
        echo Build cancelado pelo usuário.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo  BUILD CONCLUÍDO COM SUCESSO!
echo ========================================
echo.
echo Classes compiladas em: D:\Windchill\codebase\com\infoaxis
echo.
echo Para reiniciar o Windchill e aplicar as mudanças:
echo 1. Pare o servidor Windchill
echo 2. Execute: windchill stop
echo 3. Execute: windchill start
echo.

pause

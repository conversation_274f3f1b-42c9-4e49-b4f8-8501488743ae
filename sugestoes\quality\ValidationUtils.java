package com.infoaxis.integracao.core.util;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Utilitários para validações defensivas em integrações ERP.
 * Centraliza lógicas de validação comuns para manter consistência.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class ValidationUtils {
    
    // Padrões regex comuns
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    
    private static final Pattern COMPANY_CODE_PATTERN = Pattern.compile("^\\d{2,4}$");
    
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[A-Za-z0-9.-]+(:[0-9]+)?(/.*)?$");
    
    /**
     * Construtor privado para classe utilitária.
     */
    private ValidationUtils() {
        throw new UnsupportedOperationException("Classe utilitária não deve ser instanciada");
    }
    
    /**
     * Valida se string não é null nem vazia.
     * 
     * @param value Valor a ser validado
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se inválido
     */
    public static void requireNonEmpty(String value, String fieldName) throws ERPIntegrationException {
        if (value == null) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode ser null", fieldName), null);
        }
        
        if (value.trim().isEmpty()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode ser vazio", fieldName), null);
        }
    }
    
    /**
     * Valida se objeto não é null.
     * 
     * @param object Objeto a ser validado
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se null
     */
    public static void requireNonNull(Object object, String fieldName) throws ERPIntegrationException {
        if (object == null) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode ser null", fieldName), null);
        }
    }
    
    /**
     * Valida se coleção não é null nem vazia.
     * 
     * @param collection Coleção a ser validada
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se inválida
     */
    public static void requireNonEmpty(Collection<?> collection, String fieldName) 
            throws ERPIntegrationException {
        requireNonNull(collection, fieldName);
        
        if (collection.isEmpty()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode estar vazio", fieldName), null);
        }
    }
    
    /**
     * Valida se mapa não é null nem vazio.
     * 
     * @param map Mapa a ser validado
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se inválido
     */
    public static void requireNonEmpty(Map<?, ?> map, String fieldName) throws ERPIntegrationException {
        requireNonNull(map, fieldName);
        
        if (map.isEmpty()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode estar vazio", fieldName), null);
        }
    }
    
    /**
     * Valida formato de email.
     * 
     * @param email Email a ser validado
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se formato inválido
     */
    public static void requireValidEmail(String email, String fieldName) throws ERPIntegrationException {
        requireNonEmpty(email, fieldName);
        
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' deve conter um email válido", fieldName), null);
        }
    }
    
    /**
     * Valida código de empresa (2-4 dígitos).
     * 
     * @param companyCode Código a ser validado
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se formato inválido
     */
    public static void requireValidCompanyCode(String companyCode, String fieldName) 
            throws ERPIntegrationException {
        requireNonEmpty(companyCode, fieldName);
        
        if (!COMPANY_CODE_PATTERN.matcher(companyCode).matches()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' deve conter 2-4 dígitos", fieldName), null);
        }
    }
    
    /**
     * Valida formato de URL.
     * 
     * @param url URL a ser validada
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se formato inválido
     */
    public static void requireValidUrl(String url, String fieldName) throws ERPIntegrationException {
        requireNonEmpty(url, fieldName);
        
        if (!URL_PATTERN.matcher(url).matches()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' deve conter uma URL válida", fieldName), null);
        }
    }
    
    /**
     * Valida se número está dentro de um range.
     * 
     * @param value Valor a ser validado
     * @param min Valor mínimo (inclusivo)
     * @param max Valor máximo (inclusivo)
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se fora do range
     */
    public static void requireInRange(int value, int min, int max, String fieldName) 
            throws ERPIntegrationException {
        if (value < min || value > max) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' deve estar entre %d e %d (valor: %d)", 
                    fieldName, min, max, value), null);
        }
    }
    
    /**
     * Valida se string tem tamanho dentro do limite.
     * 
     * @param value String a ser validada
     * @param maxLength Tamanho máximo permitido
     * @param fieldName Nome do campo para mensagem de erro
     * @throws ERPIntegrationException se muito longa
     */
    public static void requireMaxLength(String value, int maxLength, String fieldName) 
            throws ERPIntegrationException {
        requireNonNull(value, fieldName);
        
        if (value.length() > maxLength) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' não pode ter mais que %d caracteres (atual: %d)", 
                    fieldName, maxLength, value.length()), null);
        }
    }
    
    /**
     * Valida se string atende a um padrão regex customizado.
     * 
     * @param value String a ser validada
     * @param pattern Padrão regex
     * @param fieldName Nome do campo para mensagem de erro
     * @param patternDescription Descrição do padrão para mensagem de erro
     * @throws ERPIntegrationException se não atende ao padrão
     */
    public static void requirePattern(String value, Pattern pattern, String fieldName, 
                                    String patternDescription) throws ERPIntegrationException {
        requireNonEmpty(value, fieldName);
        
        if (!pattern.matcher(value).matches()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", 
                String.format("Campo '%s' deve atender ao padrão: %s", fieldName, patternDescription), 
                null);
        }
    }
    
    /**
     * Valida múltiplas condições com mensagem customizada.
     * 
     * @param condition Condição a ser verificada
     * @param errorMessage Mensagem de erro se condição falsa
     * @throws ERPIntegrationException se condição falsa
     */
    public static void require(boolean condition, String errorMessage) throws ERPIntegrationException {
        if (!condition) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "validation", errorMessage, null);
        }
    }
}

@echo off
echo ========================================
echo Configurando VSCode para Windchill
echo ========================================

echo.
echo 1. Verificando ambiente...
set WINDCHILL_HOME=D:\Windchill

if not exist "%WINDCHILL_HOME%" (
    echo ERRO: Windchill nao encontrado em %WINDCHILL_HOME%
    pause
    exit /b 1
)

echo ✓ Windchill encontrado em %WINDCHILL_HOME%

echo.
echo 2. Verificando dependencias criticas...
set MISSING_DEPS=0

if not exist "%WINDCHILL_HOME%\lib\esi.jar" (
    echo ✗ esi.jar NAO encontrado
    set MISSING_DEPS=1
) else (
    echo ✓ esi.jar encontrado
)

if not exist "%WINDCHILL_HOME%\lib\wnc.jar" (
    echo ✗ wnc.jar NAO encontrado
    set MISSING_DEPS=1
) else (
    echo ✓ wnc.jar encontrado
)

if not exist "%WINDCHILL_HOME%\codebase\WEB-INF\lib\ieWeb.jar" (
    echo ✗ ieWeb.jar NAO encontrado
    set MISSING_DEPS=1
) else (
    echo ✓ ieWeb.jar encontrado
)

if %MISSING_DEPS%==1 (
    echo.
    echo AVISO: Algumas dependencias do Windchill nao foram encontradas.
    echo Isso pode causar problemas de reconhecimento no VSCode.
    echo.
)

echo.
echo 3. Limpando cache do Java Language Server...
for /d %%i in ("%USERPROFILE%\.vscode\extensions\redhat.java-*") do (
    if exist "%%i\server\workspaces" (
        echo Limpando: %%i\server\workspaces
        rmdir /s /q "%%i\server\workspaces" 2>nul
    )
)

echo.
echo 4. Verificando configuracoes do VSCode...
if exist ".vscode\settings.json" (
    echo ✓ Configuracoes VSCode encontradas
) else (
    echo ✗ Configuracoes VSCode NAO encontradas
    echo Execute o script de configuracao primeiro!
)

echo.
echo 5. Criando estrutura de diretorios se necessario...
if not exist "src\main\java\com\infoaxis" (
    mkdir "src\main\java\com\infoaxis" 2>nul
    echo ✓ Estrutura de diretorios criada
)

echo.
echo ========================================
echo INSTRUCOES FINAIS:
echo ========================================
echo.
echo 1. FECHE COMPLETAMENTE o VSCode (todas as janelas)
echo 2. Aguarde 10 segundos
echo 3. Reabra o VSCode nesta pasta: %CD%
echo 4. Aguarde o Java Language Server carregar (veja a barra de status)
echo 5. Abra o arquivo TestWindchillDependencies.java
echo 6. Se ainda houver erros vermelhos, execute no VSCode:
echo    - Ctrl+Shift+P
echo    - "Java: Reload Projects"
echo    - "Java: Rebuild Workspace"
echo 7. Se necessario, execute tambem:
echo    - "Developer: Reload Window"
echo.
echo DICA: Os imports do Windchill (wt.*) devem aparecer
echo       sem sublinhado vermelho se tudo estiver correto.
echo.
echo ========================================

pause

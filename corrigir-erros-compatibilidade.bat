@echo off
echo ========================================
echo CORRIGINDO ERROS DE COMPATIBILIDADE
echo ========================================
echo.

echo Procurando arquivos com problemas de import...
echo.

REM Procurar por arquivos que importam UtilDatasul incorretamente
echo === ARQUIVOS COM IMPORT DE UtilDatasul ===
findstr /s /i "import.*UtilDatasul" *.java 2>nul
echo.

REM Procurar por arquivos que usam RetornoMensagem
echo === ARQUIVOS QUE USAM RetornoMensagem ===
findstr /s /i "RetornoMensagem" *.java 2>nul
echo.

REM Procurar por arquivos que usam formataCodigoDatasul
echo === ARQUIVOS QUE USAM formataCodigoDatasul ===
findstr /s /i "formataCodigoDatasul" *.java 2>nul
echo.

REM Procurar por arquivos que usam separaRetorno
echo === ARQUIVOS QUE USAM separaRetorno ===
findstr /s /i "separaRetorno" *.java 2>nul
echo.

REM Procurar por arquivos CodeAdapter
echo === ARQUIVOS CodeAdapter ===
dir /s /b *CodeAdapter*.java 2>nul
echo.

REM Procurar por arquivos RetornoAPI
echo === ARQUIVOS RetornoAPI ===
dir /s /b *RetornoAPI*.java 2>nul
echo.

echo ========================================
echo ANALISE CONCLUIDA
echo ========================================
echo.
echo Verifique os resultados acima para identificar os arquivos com problemas.
echo.
pause

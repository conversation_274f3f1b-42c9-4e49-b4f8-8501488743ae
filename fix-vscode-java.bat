@echo off
echo ========================================
echo  Corrigindo Configuracao Java no VSCode
echo ========================================
echo.

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven

echo Verificando projeto: %PROJETO%
if not exist "%PROJETO%" (
    echo ERRO: Projeto nao encontrado!
    pause
    exit /b 1
)

cd /d "%PROJETO%"

echo.
echo 1. Criando arquivo .classpath para Eclipse/VSCode...
echo ^<?xml version="1.0" encoding="UTF-8"?^> > .classpath
echo ^<classpath^> >> .classpath
echo     ^<classpathentry kind="src" output="target/classes" path="src/main/java"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="optional" value="true"/^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath
echo     ^<classpathentry kind="src" output="target/test-classes" path="src/test/java"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="optional" value="true"/^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath
echo     ^<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath
echo     ^<classpathentry kind="con" path="org.eclipse.m2e.MAVEN_DEPENDENCY_CLASSPATH"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/esi.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/servlet.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/wnc.jar"/^> >> .classpath
echo     ^<classpathentry kind="src" path="D:/Windchill/src/com/infoaxis/core"/^> >> .classpath
echo     ^<classpathentry kind="src" path="D:/Windchill/src/com/infoaxis/esi"/^> >> .classpath
echo     ^<classpathentry kind="output" path="target/classes"/^> >> .classpath
echo ^</classpath^> >> .classpath

echo.
echo 2. Criando arquivo .project...
echo ^<?xml version="1.0" encoding="UTF-8"?^> > .project
echo ^<projectDescription^> >> .project
echo     ^<name^>integracaoDatasul-maven^</name^> >> .project
echo     ^<comment^>^</comment^> >> .project
echo     ^<projects^>^</projects^> >> .project
echo     ^<buildSpec^> >> .project
echo         ^<buildCommand^> >> .project
echo             ^<name^>org.eclipse.jdt.core.javabuilder^</name^> >> .project
echo             ^<arguments^>^</arguments^> >> .project
echo         ^</buildCommand^> >> .project
echo         ^<buildCommand^> >> .project
echo             ^<name^>org.eclipse.m2e.core.maven2Builder^</name^> >> .project
echo             ^<arguments^>^</arguments^> >> .project
echo         ^</buildCommand^> >> .project
echo     ^</buildSpec^> >> .project
echo     ^<natures^> >> .project
echo         ^<nature^>org.eclipse.jdt.core.javanature^</nature^> >> .project
echo         ^<nature^>org.eclipse.m2e.core.maven2Nature^</nature^> >> .project
echo     ^</natures^> >> .project
echo ^</projectDescription^> >> .project

echo.
echo 3. Atualizando configuracoes VSCode...
if not exist ".vscode" mkdir .vscode

echo { > .vscode\settings.json
echo     "java.configuration.updateBuildConfiguration": "automatic", >> .vscode\settings.json
echo     "java.compile.nullAnalysis.mode": "automatic", >> .vscode\settings.json
echo     "java.dependency.packagePresentation": "hierarchical", >> .vscode\settings.json
echo     "java.dependency.syncWithFolderExplorer": true, >> .vscode\settings.json
echo     "maven.executable.path": "mvn", >> .vscode\settings.json
echo     "maven.terminal.useJavaHome": true, >> .vscode\settings.json
echo     "java.project.sourcePaths": [ >> .vscode\settings.json
echo         "src/main/java", >> .vscode\settings.json
echo         "src/test/java" >> .vscode\settings.json
echo     ], >> .vscode\settings.json
echo     "java.project.referencedLibraries": [ >> .vscode\settings.json
echo         "D:/Windchill/lib/**/*.jar", >> .vscode\settings.json
echo         "D:/Windchill/codebase/WEB-INF/lib/**/*.jar", >> .vscode\settings.json
echo         "D:/eclipse/cust_Windchill_src/**/*.jar" >> .vscode\settings.json
echo     ], >> .vscode\settings.json
echo     "files.encoding": "utf8", >> .vscode\settings.json
echo     "java.project.encoding": "UTF-8", >> .vscode\settings.json
echo     "java.format.onSave.enabled": true, >> .vscode\settings.json
echo     "editor.formatOnSave": true, >> .vscode\settings.json
echo     "java.completion.enabled": true, >> .vscode\settings.json
echo     "java.completion.guessMethodArguments": true >> .vscode\settings.json
echo } >> .vscode\settings.json

echo.
echo 4. Criando diretorio target...
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

echo.
echo ========================================
echo  CORRECAO CONCLUIDA!
echo ========================================
echo.
echo Agora:
echo 1. Feche o VSCode
echo 2. Reabra o VSCode neste projeto
echo 3. Aguarde o VSCode processar (pode demorar 1-2 minutos)
echo 4. Verifique se aparece "Java Projects" na barra lateral
echo.

pause

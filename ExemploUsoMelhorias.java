package com.infoaxis.integracaoDatasul.esi;

import java.util.logging.Logger;

/**
 * Exemplo de como usar as melhorias implementadas.
 * Demonstra o uso do Strategy Pattern e tratamento de erros melhorado.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ExemploUsoMelhorias {
    
    private static final Logger LOGGER = Logger.getLogger(ExemploUsoMelhorias.class.getName());
    
    /**
     * Exemplo de uso do método melhorado de tratamento de erro.
     */
    public static void exemploTratamentoErro() {
        try {
            // Simula uma operação que pode falhar
            throw new java.net.SocketTimeoutException("Timeout na conexão");
            
        } catch (Exception e) {
            // Usa o método melhorado
            String errorMessage = CommERPHelperMelhorado.trataErroGenerico("datasul/items", e);
            LOGGER.severe("Erro tratado: " + errorMessage);
            
            // Verifica se é recuperável
            if (CommERPHelperMelhorado.isRecoverableError(e)) {
                LOGGER.info("Erro é recuperável - pode tentar novamente");
            }
            
            // Cria mensagem estruturada para logging
            String structured = CommERPHelperMelhorado.createStructuredErrorMessage(
                "datasul/items", e, "Tentativa 1 de 3");
            LOGGER.info("Log estruturado: " + structured);
        }
    }
    
    /**
     * Exemplo de uso do Strategy Pattern para diferentes ERPs.
     */
    public static void exemploStrategyPattern() {
        try {
            // Exemplo 1: Integração com Datasul
            ERPIntegrationStrategy datasulStrategy = ERPIntegrationFactory.createStrategy("DATASUL");
            
            // Testa conexão
            if (datasulStrategy.testConnection()) {
                LOGGER.info("Conexão com Datasul OK");
                
                // Executa integração
                Object request = criarRequestDatasul();
                Object result = datasulStrategy.executeIntegration(request);
                LOGGER.info("Integração Datasul concluída: " + result);
            }
            
            // Exemplo 2: Integração com Senior
            ERPIntegrationStrategy seniorStrategy = ERPIntegrationFactory.createStrategy("SENIOR");
            
            if (seniorStrategy.testConnection()) {
                LOGGER.info("Conexão com Senior OK");
                
                Object request = criarRequestSenior();
                Object result = seniorStrategy.executeIntegration(request);
                LOGGER.info("Integração Senior concluída: " + result);
            }
            
            // Exemplo 3: Verificar ERPs suportados
            LOGGER.info("ERPs suportados:");
            for (ERPIntegrationStrategy.ERPType type : ERPIntegrationFactory.getSupportedERPTypes()) {
                LOGGER.info("- " + type.getDisplayName());
            }
            
            // Exemplo 4: Verificar se ERP é suportado
            if (ERPIntegrationFactory.isSupported("PROTHEUS")) {
                LOGGER.info("Protheus é suportado");
            }
            
        } catch (Exception e) {
            String errorMessage = CommERPHelperMelhorado.trataErroGenerico("strategy_example", e);
            LOGGER.severe("Erro no exemplo Strategy: " + errorMessage);
        }
    }
    
    /**
     * Exemplo de como adicionar um novo ERP facilmente.
     */
    public static void exemploAdicionarNovoERP() {
        LOGGER.info("Para adicionar um novo ERP (ex: SAP):");
        LOGGER.info("1. Criar SAPIntegrationStrategy implementando ERPIntegrationStrategy");
        LOGGER.info("2. Adicionar case SAP no ERPIntegrationFactory.createNewStrategy()");
        LOGGER.info("3. Adicionar SAP no array getSupportedERPTypes()");
        LOGGER.info("4. Pronto! O novo ERP estará disponível para uso");
    }
    
    /**
     * Exemplo de uso das validações defensivas.
     */
    public static void exemploValidacoes() {
        try {
            // Validação de string não vazia
            CommERPHelperMelhorado.validateNotEmpty("valor_valido", "parametro");
            LOGGER.info("Validação de string passou");
            
            // Validação de objeto não nulo
            Object objeto = new Object();
            CommERPHelperMelhorado.validateNotNull(objeto, "objeto");
            LOGGER.info("Validação de objeto passou");
            
            // Exemplo de validação que falha
            try {
                CommERPHelperMelhorado.validateNotEmpty("", "parametro_vazio");
            } catch (IllegalArgumentException e) {
                LOGGER.warning("Validação capturou erro esperado: " + e.getMessage());
            }
            
        } catch (Exception e) {
            String errorMessage = CommERPHelperMelhorado.trataErroGenerico("validacao_example", e);
            LOGGER.severe("Erro no exemplo de validação: " + errorMessage);
        }
    }
    
    /**
     * Método principal para executar todos os exemplos.
     */
    public static void main(String[] args) {
        LOGGER.info("=== Exemplos de Uso das Melhorias ===");
        
        LOGGER.info("\n1. Exemplo de Tratamento de Erro:");
        exemploTratamentoErro();
        
        LOGGER.info("\n2. Exemplo de Strategy Pattern:");
        exemploStrategyPattern();
        
        LOGGER.info("\n3. Exemplo de Adição de Novo ERP:");
        exemploAdicionarNovoERP();
        
        LOGGER.info("\n4. Exemplo de Validações:");
        exemploValidacoes();
        
        LOGGER.info("\n=== Fim dos Exemplos ===");
    }
    
    // Métodos auxiliares para criar requests de exemplo
    private static Object criarRequestDatasul() {
        // TODO: Implementar criação de request específico do Datasul
        return "Request Datasul de exemplo";
    }
    
    private static Object criarRequestSenior() {
        // TODO: Implementar criação de request específico do Senior
        return "Request Senior de exemplo";
    }
}

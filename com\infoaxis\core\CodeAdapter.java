/** Adaptador para conversões seguras entre tipos de código (String ↔ int).
 * Implementa o padrão Adapter para resolver incompatibilidades de tipo
 * causadas pela migração RetornoMensagem → RetornoAPI.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | Fábio M Valente  | Criação do adaptador para compatibilidade de tipos.
 */
package com.infoaxis.core;

public class CodeAdapter {
    
    /**
     * Converte código de RetornoAPI para formato String.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como String
     */
    public static String getCodigoAsString(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            return "-1";
        }
        
        return retornoAPI.getCodigoAsString();
    }
    
    /**
     * Converte código de RetornoAPI para Integer.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como Integer
     */
    public static Integer getCodigoAsInteger(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            return -1;
        }
        
        return retornoAPI.getCodigo();
    }
    
    /**
     * Verifica se o código indica sucesso (>= 0).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return true se código indica sucesso
     */
    public static boolean isSucesso(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            return false;
        }
        return retornoAPI.getCodigo() >= 0;
    }
    
    /**
     * Verifica se o código indica erro (< 0).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return true se código indica erro
     */
    public static boolean isErro(RetornoAPI retornoAPI) {
        return !isSucesso(retornoAPI);
    }
    
    /**
     * Método de conveniência para comparações de código.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param valorComparacao Valor para comparar
     * @return true se códigos são iguais
     */
    public static boolean codigoEquals(RetornoAPI retornoAPI, int valorComparacao) {
        if (retornoAPI == null) {
            return valorComparacao == -1;
        }
        return retornoAPI.getCodigo() == valorComparacao;
    }
    
    /**
     * Método de conveniência para comparações de código com String.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param valorComparacao Valor String para comparar
     * @return true se códigos são iguais
     */
    public static boolean codigoEquals(RetornoAPI retornoAPI, String valorComparacao) {
        if (retornoAPI == null || valorComparacao == null) {
            return false;
        }
        return retornoAPI.getCodigoAsString().equals(valorComparacao);
    }
    
    // ========== MÉTODOS PARA MIGRAÇÃO GRADUAL ==========
    
    /**
     * Substitui o padrão: addItem.get().getCodigo() quando era String.
     * Use este método durante a migração.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como String (compatibilidade)
     * @deprecated Use getCodigoAsString() diretamente
     */
    @Deprecated
    public static String getCodigo(RetornoAPI retornoAPI) {
        return getCodigoAsString(retornoAPI);
    }
}

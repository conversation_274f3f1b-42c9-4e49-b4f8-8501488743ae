eclipse.preferences.version=1
org.eclipse.jdt.core.builder.resourceCopyExclusionFilter=*.*~,*.bak,*.cat,*.checkedout,*.classpath,*.contrib*,*.copyarea.db,*.cup,*.flex,*.form,*.hijacked,*.java,*.jj,*.keep*,*.log,*.mData,*.mak,*.mdl,*.merge,*.mjava,*.mkelem,*.nbattrs,*.nbintdb,*.notes,*.notes.txt,*.project,*.rbInfo,*.unloaded/,*.vbp,*.vcproj*,*.ve2,*.vep,*.vpj,*.vssscc,*.wsp,Copy of *,Makefile*,Package.doc,build*.xml,doc-files/,lost+found/,makefile*,makefile.sub,overview.html,package.doc,package.html,relsrc.inc,*.class.path,*.ptcdarinfo,*.ptctarinfo,bin/,cb_registry/,conf/,db/,dca/,*.xconf,index.bpi,loadFiles/,lost+found,opt/,tasks/,testLoadFiles/,utilities/
org.eclipse.jdt.core.compiler.annotation.missingNonNullByDefaultAnnotation=ignore
org.eclipse.jdt.core.compiler.annotation.nonnull=javax.annotation.Nonnull
org.eclipse.jdt.core.compiler.annotation.nonnullbydefault=javax.annotation.ParametersAreNonnullByDefault
org.eclipse.jdt.core.compiler.annotation.nullable=javax.annotation.Nullable
org.eclipse.jdt.core.compiler.annotation.nullanalysis=enabled
org.eclipse.jdt.core.compiler.problem.nullAnnotationInferenceConflict=warning
org.eclipse.jdt.core.compiler.problem.nullReference=warning
org.eclipse.jdt.core.compiler.problem.nullSpecViolation=warning
org.eclipse.jdt.core.compiler.problem.nullUncheckedConversion=ignore
org.eclipse.jdt.core.compiler.problem.overridingPackageDefaultMethod=ignore
org.eclipse.jdt.core.compiler.problem.potentialNullReference=warning
org.eclipse.jdt.core.compiler.problem.rawTypeReference=ignore
org.eclipse.jdt.core.compiler.problem.syntacticNullAnalysisForFields=enabled
org.eclipse.jdt.core.compiler.problem.unhandledWarningToken=ignore
org.eclipse.jdt.core.compiler.problem.unusedWarningToken=ignore
org.eclipse.jdt.core.compiler.processAnnotations=enabled

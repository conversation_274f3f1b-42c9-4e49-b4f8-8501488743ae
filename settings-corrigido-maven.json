{"java.configuration.updateBuildConfiguration": "automatic", "java.import.maven.enabled": true, "java.import.gradle.enabled": false, "java.compile.nullAnalysis.mode": "disabled", "java.dependency.packagePresentation": "hierarchical", "java.dependency.syncWithFolderExplorer": false, "maven.executable.path": "mvn", "maven.terminal.useJavaHome": true, "maven.downloadSources": true, "java.project.sourcePaths": ["src/main/java", "src/test/java", "D:/Windchill/src"], "java.project.referencedLibraries": {"include": ["D:/Windchill/lib/*.jar", "D:/Windchill/codebase/WEB-INF/lib/*.jar", "D:/Windchill/srclib/**/*.jar"], "exclude": ["**/test/**"], "sources": {"D:/Windchill/lib/*.jar": "D:/Windchill/src/**", "D:/Windchill/codebase/WEB-INF/lib/*.jar": "D:/Windchill/src/**"}}, "java.project.outputPath": "D:/Windchill/codebase", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -Xmx4G -Xms512m -Dsun.zip.disableMemoryMapping=true", "files.encoding": "utf8", "java.project.encoding": "UTF-8", "java.format.onSave.enabled": true, "editor.formatOnSave": true, "java.completion.enabled": true, "java.completion.guessMethodArguments": true, "java.signatureHelp.enabled": true, "java.contentProvider.preferred": "fernflower", "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99, "java.eclipse.downloadSources": true, "java.maven.downloadSources": true, "java.debug.settings.onBuildFailureProceed": true, "java.debug.settings.showQualifiedNames": false, "java.project.resourceFilters": [{"node": "node_modules", "type": "excludeFolder"}, {"node": ".git", "type": "excludeFolder"}]}
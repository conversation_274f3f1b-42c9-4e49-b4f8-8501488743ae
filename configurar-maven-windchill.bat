@echo off
echo ========================================
echo Configurando Projeto Maven + Windchill
echo ========================================

set PROJETO_MAVEN=%CD%\projeto-maven
set DESTINO_PROJETO=D:\Projetos\Java\integracaoDatasul

echo.
echo 1. Verificando estrutura do projeto Maven...
if not exist "%PROJETO_MAVEN%" (
    echo ERRO: Pasta projeto-maven nao encontrada!
    pause
    exit /b 1
)

echo ✓ Projeto Maven encontrado em: %PROJETO_MAVEN%

echo.
echo 2. Criando diretorio de destino se necessario...
if not exist "D:\Projetos\Java" (
    mkdir "D:\Projetos\Java" 2>nul
)

echo.
echo 3. Copiando projeto Maven para destino...
if exist "%DESTINO_PROJETO%" (
    echo Projeto ja existe em %DESTINO_PROJETO%
    echo Deseja sobrescrever? (S/N)
    set /p RESPOSTA=
    if /i "%RESPOSTA%"=="S" (
        rmdir /s /q "%DESTINO_PROJETO%" 2>nul
    ) else (
        echo Operacao cancelada.
        pause
        exit /b 0
    )
)

xcopy "%PROJETO_MAVEN%" "%DESTINO_PROJETO%" /E /I /H /Y >nul
echo ✓ Projeto copiado para %DESTINO_PROJETO%

echo.
echo 4. Criando configuracoes VSCode para Maven + Windchill...
if not exist "%DESTINO_PROJETO%\.vscode" (
    mkdir "%DESTINO_PROJETO%\.vscode" 2>nul
)

echo Criando settings.json para Maven + Windchill...
(
echo {
echo     "java.configuration.updateBuildConfiguration": "automatic",
echo     "java.import.maven.enabled": true,
echo     "java.import.gradle.enabled": false,
echo     "java.project.referencedLibraries": {
echo         "include": [
echo             "D:/Windchill/lib/*.jar",
echo             "D:/Windchill/codebase/WEB-INF/lib/*.jar",
echo             "D:/Windchill/srclib/**/*.jar"
echo         ]
echo     },
echo     "java.jdt.ls.vmargs": "-XX:+UseParallelGC -Xmx4G -Xms512m",
echo     "java.dependency.packagePresentation": "hierarchical",
echo     "java.compile.nullAnalysis.mode": "disabled",
echo     "java.completion.enabled": true,
echo     "java.signatureHelp.enabled": true,
echo     "maven.executable.path": "mvn",
echo     "maven.terminal.useJavaHome": true,
echo     "java.project.encoding": "UTF-8"
echo }
) > "%DESTINO_PROJETO%\.vscode\settings.json"

echo ✓ settings.json criado

echo.
echo 5. Criando launch.json para debug...
(
echo {
echo     "version": "0.2.0",
echo     "configurations": [
echo         {
echo             "type": "java",
echo             "name": "Current File",
echo             "request": "launch",
echo             "mainClass": "${file}",
echo             "vmArgs": "-Xmx2G -Xms512m"
echo         },
echo         {
echo             "type": "java",
echo             "name": "Test Datasul Integration",
echo             "request": "launch",
echo             "mainClass": "com.infoaxis.integracaoDatasul.esi.ExemploUsoMelhorias",
echo             "projectName": "windchill-erp-integration",
echo             "vmArgs": "-Xmx2G -Xms512m"
echo         }
echo     ]
echo }
) > "%DESTINO_PROJETO%\.vscode\launch.json"

echo ✓ launch.json criado

echo.
echo 6. Verificando dependencias do Windchill...
set WINDCHILL_HOME=D:\Windchill
if exist "%WINDCHILL_HOME%\lib\esi.jar" (
    echo ✓ esi.jar encontrado
) else (
    echo ✗ esi.jar NAO encontrado
)

if exist "%WINDCHILL_HOME%\lib\wnc.jar" (
    echo ✓ wnc.jar encontrado
) else (
    echo ✗ wnc.jar NAO encontrado
)

echo.
echo ========================================
echo PROJETO MAVEN + WINDCHILL CONFIGURADO!
echo ========================================
echo.
echo Proximo passo:
echo 1. Abra o VSCode em: %DESTINO_PROJETO%
echo 2. Aguarde o Maven baixar dependencias
echo 3. Aguarde o Java Language Server carregar
echo 4. Teste os imports do Windchill
echo.
echo Comando para abrir:
echo cd /d "%DESTINO_PROJETO%" ^&^& code .
echo.
echo ========================================

pause

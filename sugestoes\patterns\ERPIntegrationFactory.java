package com.infoaxis.integracao.core.factory;

import com.infoaxis.integracao.core.service.ERPIntegrationStrategy;
import com.infoaxis.integracao.core.service.ERPIntegrationStrategy.ERPType;
import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.erp.datasul.DatasulIntegrationStrategy;
import com.infoaxis.integracao.erp.senior.SeniorIntegrationStrategy;
import com.infoaxis.integracao.erp.protheus.ProtheusIntegrationStrategy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Factory para criação de estratégias de integração ERP.
 * Implementa padrão Factory com cache de instâncias.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class ERPIntegrationFactory {
    
    private static final Logger LOGGER = Logger.getLogger(ERPIntegrationFactory.class.getName());
    
    // Cache thread-safe de estratégias
    private static final Map<ERPType, ERPIntegrationStrategy> strategyCache = 
        new ConcurrentHashMap<>();
    
    private ERPIntegrationFactory() {
        // Classe utilitária - construtor privado
    }
    
    /**
     * Cria ou retorna estratégia cached para o tipo de ERP especificado.
     * 
     * @param erpType Tipo do ERP
     * @return Estratégia de integração
     * @throws ERPIntegrationException se tipo não suportado
     */
    public static ERPIntegrationStrategy createStrategy(ERPType erpType) 
            throws ERPIntegrationException {
        
        if (erpType == null) {
            throw new ERPIntegrationException("FACTORY_ERROR", "factory", 
                "Tipo de ERP não pode ser nulo", null);
        }
        
        // Verifica cache primeiro
        ERPIntegrationStrategy cachedStrategy = strategyCache.get(erpType);
        if (cachedStrategy != null) {
            LOGGER.fine("Retornando estratégia cached para: " + erpType);
            return cachedStrategy;
        }
        
        // Cria nova estratégia
        ERPIntegrationStrategy strategy = createNewStrategy(erpType);
        
        // Adiciona ao cache
        strategyCache.put(erpType, strategy);
        LOGGER.info("Nova estratégia criada e cached para: " + erpType);
        
        return strategy;
    }
    
    /**
     * Cria nova instância de estratégia baseada no tipo.
     * 
     * @param erpType Tipo do ERP
     * @return Nova estratégia
     * @throws ERPIntegrationException se tipo não suportado
     */
    private static ERPIntegrationStrategy createNewStrategy(ERPType erpType) 
            throws ERPIntegrationException {
        
        try {
            switch (erpType) {
                case DATASUL:
                    return createDatasulStrategy();
                    
                case SENIOR:
                    return createSeniorStrategy();
                    
                case PROTHEUS:
                    return createProtheusStrategy();
                    
                default:
                    throw new ERPIntegrationException("UNSUPPORTED_ERP", "factory", 
                        "Tipo de ERP não suportado: " + erpType, null);
            }
        } catch (Exception e) {
            throw new ERPIntegrationException("FACTORY_ERROR", "factory", 
                "Erro ao criar estratégia para " + erpType, e);
        }
    }
    
    /**
     * Cria estratégia específica para Datasul.
     * 
     * @return Estratégia Datasul configurada
     */
    private static ERPIntegrationStrategy createDatasulStrategy() {
        // Aqui você injetaria as dependências necessárias
        // Por exemplo, através de um container IoC ou configuração
        return new DatasulIntegrationStrategy(
            DatasulConnectionManagerFactory.create(),
            DatasulDataMapperFactory.create()
        );
    }
    
    /**
     * Cria estratégia específica para Senior.
     * 
     * @return Estratégia Senior configurada
     */
    private static ERPIntegrationStrategy createSeniorStrategy() {
        return new SeniorIntegrationStrategy(
            SeniorConnectionManagerFactory.create(),
            SeniorDataMapperFactory.create()
        );
    }
    
    /**
     * Cria estratégia específica para Protheus.
     * 
     * @return Estratégia Protheus configurada
     */
    private static ERPIntegrationStrategy createProtheusStrategy() {
        return new ProtheusIntegrationStrategy(
            ProtheusConnectionManagerFactory.create(),
            ProtheusDataMapperFactory.create()
        );
    }
    
    /**
     * Limpa cache de estratégias (útil para testes ou reconfiguração).
     */
    public static void clearCache() {
        strategyCache.clear();
        LOGGER.info("Cache de estratégias limpo");
    }
    
    /**
     * Retorna tipos de ERP suportados.
     * 
     * @return Array com tipos suportados
     */
    public static ERPType[] getSupportedERPTypes() {
        return new ERPType[]{ERPType.DATASUL, ERPType.SENIOR, ERPType.PROTHEUS};
    }
    
    /**
     * Verifica se um tipo de ERP é suportado.
     * 
     * @param erpType Tipo a verificar
     * @return true se suportado
     */
    public static boolean isSupported(ERPType erpType) {
        if (erpType == null) return false;
        
        for (ERPType supported : getSupportedERPTypes()) {
            if (supported == erpType) {
                return true;
            }
        }
        return false;
    }
}

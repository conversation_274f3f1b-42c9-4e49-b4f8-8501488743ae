@echo off
echo ========================================
echo Testando Projeto Maven + Windchill
echo ========================================

set DESTINO_PROJETO=D:\Projetos\Java\integracaoDatasul

echo.
echo 1. Verificando se o projeto existe...
if not exist "%DESTINO_PROJETO%" (
    echo ERRO: Projeto nao encontrado em %DESTINO_PROJETO%
    echo Execute primeiro: configurar-maven-windchill.bat
    pause
    exit /b 1
)

echo ✓ Projeto encontrado em %DESTINO_PROJETO%

echo.
echo 2. Navegando para o projeto...
cd /d "%DESTINO_PROJETO%"

echo.
echo 3. Verificando Maven...
mvn --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ✗ Maven nao encontrado no PATH
    echo Instale o Maven ou adicione ao PATH
    pause
    exit /b 1
) else (
    echo ✓ Maven encontrado
    mvn --version
)

echo.
echo 4. Verificando dependencias do Windchill...
if exist "D:\Windchill\lib\esi.jar" (
    echo ✓ esi.jar encontrado
) else (
    echo ✗ esi.jar NAO encontrado
    set MISSING_DEPS=1
)

if exist "D:\Windchill\lib\wnc.jar" (
    echo ✓ wnc.jar encontrado
) else (
    echo ✗ wnc.jar NAO encontrado
    set MISSING_DEPS=1
)

if exist "D:\Windchill\codebase\WEB-INF\lib\ieWeb.jar" (
    echo ✓ ieWeb.jar encontrado
) else (
    echo ✗ ieWeb.jar NAO encontrado
    set MISSING_DEPS=1
)

if defined MISSING_DEPS (
    echo.
    echo AVISO: Algumas dependencias do Windchill estao faltando!
    echo Isso pode causar problemas de compilacao.
    echo.
)

echo.
echo 5. Testando compilacao Maven...
echo Executando: mvn clean compile
mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo.
    echo ✗ ERRO na compilacao Maven!
    echo Verifique as dependencias do Windchill no pom.xml
    echo.
) else (
    echo.
    echo ✓ Compilacao Maven bem-sucedida!
    echo.
)

echo.
echo 6. Abrindo VSCode no projeto...
echo Executando: code .
start "" code .

echo.
echo ========================================
echo TESTE CONCLUIDO
echo ========================================
echo.
echo O VSCode foi aberto no projeto Maven.
echo.
echo Proximos passos no VSCode:
echo 1. Aguarde o Maven resolver dependencias
echo 2. Aguarde o Java Language Server carregar
echo 3. Abra um arquivo .java
echo 4. Verifique se os imports wt.* funcionam
echo 5. Se houver problemas, execute:
echo    - Ctrl+Shift+P
echo    - "Java: Reload Projects"
echo    - "Maven: Reload Projects"
echo.
echo ========================================

pause

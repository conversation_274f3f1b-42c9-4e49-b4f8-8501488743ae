@echo off
REM ============================================================================
REM Script de Setup do Ambiente de Desenvolvimento
REM ============================================================================

echo.
echo ========================================
echo  Setup Ambiente de Desenvolvimento
echo ========================================
echo.

REM Verificar pré-requisitos
echo Verificando pré-requisitos...

REM Java
java -version >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Java não encontrado!
    echo Instale o Java 17 ou superior.
    pause
    exit /b 1
)

REM Maven
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Maven não encontrado!
    echo Instale o Maven e adicione ao PATH.
    pause
    exit /b 1
)

REM Windchill
if not exist "D:\Windchill" (
    echo ERRO: Windchill não encontrado em D:\Windchill
    pause
    exit /b 1
)

echo Pré-requisitos OK!
echo.

REM Criar estrutura de diretórios
echo Criando estrutura de diretórios...

set PROJECT_ROOT=D:\Projetos\Java\windchill-erp-integration

mkdir "%PROJECT_ROOT%" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\core\exception" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\core\util" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\core\config" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\core\service" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\core\model" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\erp\datasul" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\erp\senior" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\erp\protheus" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\integration\api" 2>nul
mkdir "%PROJECT_ROOT%\src\main\java\com\infoaxis\integracao\integration\connector" 2>nul
mkdir "%PROJECT_ROOT%\src\main\resources" 2>nul
mkdir "%PROJECT_ROOT%\src\test\java\com\infoaxis\integracao" 2>nul
mkdir "%PROJECT_ROOT%\src\test\resources" 2>nul
mkdir "%PROJECT_ROOT%\docs\api" 2>nul
mkdir "%PROJECT_ROOT%\docs\setup" 2>nul
mkdir "%PROJECT_ROOT%\docs\examples" 2>nul
mkdir "%PROJECT_ROOT%\scripts" 2>nul
mkdir "%PROJECT_ROOT%\.vscode" 2>nul

echo Estrutura de diretórios criada!
echo.

REM Copiar arquivos de configuração
echo Copiando arquivos de configuração...

REM Navegar para o diretório atual do script
cd /d "%~dp0"

REM Copiar pom.xml
if exist "..\pom.xml" (
    copy "..\pom.xml" "%PROJECT_ROOT%\" >nul
    echo - pom.xml copiado
)

REM Copiar scripts
if exist "build.bat" (
    copy "build.bat" "%PROJECT_ROOT%\scripts\" >nul
    echo - build.bat copiado
)

if exist "deploy.bat" (
    copy "deploy.bat" "%PROJECT_ROOT%\scripts\" >nul
    echo - deploy.bat copiado
)

echo.
echo Criando arquivos de configuração...

REM Criar .gitignore
echo # Maven> "%PROJECT_ROOT%\.gitignore"
echo target/>> "%PROJECT_ROOT%\.gitignore"
echo *.jar>> "%PROJECT_ROOT%\.gitignore"
echo *.war>> "%PROJECT_ROOT%\.gitignore"
echo *.ear>> "%PROJECT_ROOT%\.gitignore"
echo.>> "%PROJECT_ROOT%\.gitignore"
echo # IDE>> "%PROJECT_ROOT%\.gitignore"
echo .vscode/settings.json>> "%PROJECT_ROOT%\.gitignore"
echo .idea/>> "%PROJECT_ROOT%\.gitignore"
echo *.iml>> "%PROJECT_ROOT%\.gitignore"
echo.>> "%PROJECT_ROOT%\.gitignore"
echo # OS>> "%PROJECT_ROOT%\.gitignore"
echo .DS_Store>> "%PROJECT_ROOT%\.gitignore"
echo Thumbs.db>> "%PROJECT_ROOT%\.gitignore"
echo.>> "%PROJECT_ROOT%\.gitignore"
echo # Logs>> "%PROJECT_ROOT%\.gitignore"
echo *.log>> "%PROJECT_ROOT%\.gitignore"

REM Criar README.md básico
echo # Windchill ERP Integration> "%PROJECT_ROOT%\README.md"
echo.>> "%PROJECT_ROOT%\README.md"
echo Biblioteca para integração do Windchill com sistemas ERP.>> "%PROJECT_ROOT%\README.md"
echo.>> "%PROJECT_ROOT%\README.md"
echo ## Build>> "%PROJECT_ROOT%\README.md"
echo ```bash>> "%PROJECT_ROOT%\README.md"
echo mvn clean compile>> "%PROJECT_ROOT%\README.md"
echo ```>> "%PROJECT_ROOT%\README.md"

echo.
echo ========================================
echo  SETUP CONCLUÍDO COM SUCESSO!
echo ========================================
echo.
echo Projeto criado em: %PROJECT_ROOT%
echo.
echo PRÓXIMOS PASSOS:
echo 1. Abra o VSCode no diretório do projeto
echo 2. Configure as extensões Java
echo 3. Execute o primeiro build: scripts\build.bat
echo 4. Migre seu código existente
echo.
echo Para abrir no VSCode:
echo code "%PROJECT_ROOT%"
echo.

pause

package com.infoaxis.integracao.core.exception;

/**
 * Exceção base para todas as operações de integração ERP.
 * Fornece estrutura comum para tratamento de erros específicos de integração.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ERPIntegrationException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private final String errorCode;
    private final String endPoint;
    private final Object[] parameters;
    
    /**
     * Construtor básico com mensagem.
     * 
     * @param message Mensagem de erro
     */
    public ERPIntegrationException(String message) {
        super(message);
        this.errorCode = "GENERIC_ERROR";
        this.endPoint = null;
        this.parameters = null;
    }
    
    /**
     * Construtor com mensagem e causa.
     * 
     * @param message Mensagem de erro
     * @param cause Causa raiz da exceção
     */
    public ERPIntegrationException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GENERIC_ERROR";
        this.endPoint = null;
        this.parameters = null;
    }
    
    /**
     * Construtor completo para contexto de integração.
     * 
     * @param errorCode Código específico do erro
     * @param endPoint Endpoint onde ocorreu o erro
     * @param message Mensagem de erro
     * @param cause Causa raiz da exceção
     * @param parameters Parâmetros adicionais para contexto
     */
    public ERPIntegrationException(String errorCode, String endPoint, String message, 
                                 Throwable cause, Object... parameters) {
        super(message, cause);
        this.errorCode = errorCode;
        this.endPoint = endPoint;
        this.parameters = parameters;
    }
    
    /**
     * Retorna o código específico do erro.
     * 
     * @return Código do erro
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * Retorna o endpoint onde ocorreu o erro.
     * 
     * @return Endpoint do erro
     */
    public String getEndPoint() {
        return endPoint;
    }
    
    /**
     * Retorna parâmetros adicionais do contexto do erro.
     * 
     * @return Array de parâmetros
     */
    public Object[] getParameters() {
        return parameters != null ? parameters.clone() : null;
    }
    
    /**
     * Cria uma representação detalhada do erro para logging.
     * 
     * @return String formatada com detalhes do erro
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("Erro de Integração ERP: ");
        sb.append(getMessage());
        
        if (errorCode != null) {
            sb.append(" [Código: ").append(errorCode).append("]");
        }
        
        if (endPoint != null) {
            sb.append(" [Endpoint: ").append(endPoint).append("]");
        }
        
        if (parameters != null && parameters.length > 0) {
            sb.append(" [Parâmetros: ");
            for (int i = 0; i < parameters.length; i++) {
                if (i > 0) sb.append(", ");
                sb.append(parameters[i]);
            }
            sb.append("]");
        }
        
        return sb.toString();
    }
}

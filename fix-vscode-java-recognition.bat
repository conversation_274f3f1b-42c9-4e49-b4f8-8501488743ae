@echo off
echo ========================================
echo Corrigindo Reconhecimento Java no VSCode
echo ========================================

echo.
echo 1. Parando processos Java do VSCode...
taskkill /f /im java.exe 2>nul
taskkill /f /im javaw.exe 2>nul
echo Processos Java finalizados.

echo.
echo 2. Limpando cache completo do Java Language Server...
for /d %%i in ("%USERPROFILE%\.vscode\extensions\redhat.java-*") do (
    if exist "%%i\server\workspaces" (
        echo Limpando cache em: %%i\server\workspaces
        rmdir /s /q "%%i\server\workspaces" 2>nul
    )
)

echo.
echo 3. Limpando cache do VSCode...
if exist "%APPDATA%\Code\User\workspaceStorage" (
    echo Limpando workspace storage...
    for /d %%i in ("%APPDATA%\Code\User\workspaceStorage\*") do (
        if exist "%%i\state.vscdb" (
            del /q "%%i\state.vscdb" 2>nul
        )
    )
)

echo.
echo 4. Removendo arquivos temporarios do projeto...
if exist ".vscode\.browse.VC.db" del /q ".vscode\.browse.VC.db" 2>nul
if exist ".classpath~" del /q ".classpath~" 2>nul
if exist ".project~" del /q ".project~" 2>nul

echo.
echo 5. Criando arquivo de configuracao adicional...
echo # Configuracao adicional para reconhecimento Java > .vscode\java.properties
echo java.home=%JAVA_HOME% >> .vscode\java.properties
echo windchill.home=D:/Windchill >> .vscode\java.properties

echo.
echo 6. Verificando estrutura de diretorios...
if not exist "src\main\java" (
    echo Criando estrutura Maven padrao...
    mkdir "src\main\java" 2>nul
    mkdir "src\main\resources" 2>nul
    mkdir "src\test\java" 2>nul
    mkdir "src\test\resources" 2>nul
)

echo.
echo ========================================
echo INSTRUCOES PARA COMPLETAR A CORRECAO:
echo ========================================
echo.
echo 1. Feche COMPLETAMENTE o VSCode (todas as janelas)
echo 2. Aguarde 10 segundos
echo 3. Reabra o VSCode nesta pasta
echo 4. Aguarde o Java Language Server inicializar (barra de status)
echo 5. Pressione Ctrl+Shift+P e execute:
echo    - "Java: Reload Projects"
echo    - "Java: Rebuild Workspace"
echo 6. Se ainda houver problemas, execute:
echo    - "Developer: Reload Window"
echo.
echo ========================================

pause

/** Adaptador para operações específicas do Datasul com RetornoAPI.
 * Implementa o padrão Adapter para encapsular lógicas específicas do Datasul
 * sem contaminar o domínio genérico core.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | Fábio M Valente  | Criação do adaptador específico Datasul.
 */
package com.infoaxis.integracaoDatasul.adapters;

import com.infoaxis.core.RetornoAPI;
import com.infoaxis.integracaoDatasul.core.UtilDatasul;
import java.util.logging.Logger;

/**
 * Adapter para operações específicas do Datasul com RetornoAPI.
 * 
 * Esta classe encapsula toda a lógica específica do Datasul para trabalhar
 * com RetornoAPI, mantendo o domínio core limpo e genérico.
 * 
 * Funcionalidades:
 * - Formatação de códigos específica do Datasul
 * - Validação de sucesso baseada em regras Datasul
 * - Conversões e transformações específicas
 * - Logging contextualizado para Datasul
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class DatasulRetornoAdapter {
    
    private static final Logger LOGGER = Logger.getLogger(DatasulRetornoAdapter.class.getName());
    
    // Constantes específicas do Datasul
    private static final int DATASUL_SUCESSO = 0;
    private static final int DATASUL_ERRO_GENERICO = -1;
    private static final int DATASUL_ERRO_VALIDACAO = -2;
    private static final int DATASUL_ERRO_CONEXAO = -3;
    
    private final RetornoAPI retornoAPI;
    
    /**
     * Construtor do adapter.
     * 
     * @param retornoAPI Instância de RetornoAPI a ser adaptada
     * @throws IllegalArgumentException se retornoAPI for null
     */
    public DatasulRetornoAdapter(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            throw new IllegalArgumentException("RetornoAPI não pode ser null");
        }
        this.retornoAPI = retornoAPI;
        
        LOGGER.fine("DatasulRetornoAdapter criado para código: " + retornoAPI.getCodigo());
    }
    
    /**
     * Retorna o código formatado segundo padrões do Datasul.
     * Aplica formatação específica para códigos Datasul.
     * 
     * @return Código formatado para Datasul
     */
    public String getCodigoFormatado() {
        String codigo = retornoAPI.getCodigoAsString();
        String formatado = UtilDatasul.formataCodigoDatasul(codigo, true);
        
        LOGGER.fine("Código formatado para Datasul: " + codigo + " -> " + formatado);
        return formatado;
    }
    
    /**
     * Verifica se o retorno indica sucesso segundo regras do Datasul.
     * No Datasul, código 0 = sucesso, qualquer outro valor = erro.
     * 
     * @return true se indica sucesso no contexto Datasul
     */
    public boolean isSuccessoDatasul() {
        boolean sucesso = retornoAPI.getCodigo() == DATASUL_SUCESSO;
        
        LOGGER.fine("Verificação sucesso Datasul - Código: " + retornoAPI.getCodigo() + 
                   ", Sucesso: " + sucesso);
        return sucesso;
    }
    
    /**
     * Verifica se o retorno indica erro segundo regras do Datasul.
     * 
     * @return true se indica erro no contexto Datasul
     */
    public boolean isErroDatasul() {
        return !isSuccessoDatasul();
    }
    
    /**
     * Retorna o tipo de erro específico do Datasul.
     * 
     * @return Tipo de erro baseado no código
     */
    public TipoErroDatasul getTipoErro() {
        int codigo = retornoAPI.getCodigo();
        
        if (codigo == DATASUL_SUCESSO) {
            return TipoErroDatasul.NENHUM;
        } else if (codigo == DATASUL_ERRO_VALIDACAO) {
            return TipoErroDatasul.VALIDACAO;
        } else if (codigo == DATASUL_ERRO_CONEXAO) {
            return TipoErroDatasul.CONEXAO;
        } else {
            return TipoErroDatasul.GENERICO;
        }
    }
    
    /**
     * Retorna mensagem formatada para logs do Datasul.
     * Inclui contexto específico do Datasul.
     * 
     * @return Mensagem formatada para logs
     */
    public String getMensagemFormatadaParaLog() {
        String tipoErro = getTipoErro().getDescricao();
        String status = isSuccessoDatasul() ? "SUCESSO" : "ERRO";
        
        return String.format("[DATASUL-%s] Código: %s | Tipo: %s | Mensagem: %s",
                           status,
                           getCodigoFormatado(),
                           tipoErro,
                           retornoAPI.getMensagem());
    }
    
    /**
     * Cria um novo RetornoAPI com formatação específica do Datasul.
     * Útil para padronizar retornos em operações Datasul.
     * 
     * @param codigo Código específico do Datasul
     * @param mensagem Mensagem descritiva
     * @return Nova instância de RetornoAPI formatada para Datasul
     */
    public static RetornoAPI criarRetornoDatasul(int codigo, String mensagem) {
        String mensagemFormatada = String.format("[DATASUL] %s", mensagem != null ? mensagem : "");
        RetornoAPI retorno = new RetornoAPI(codigo, mensagemFormatada);
        
        LOGGER.fine("RetornoAPI criado para Datasul - Código: " + codigo + 
                   ", Mensagem: " + mensagemFormatada);
        return retorno;
    }
    
    /**
     * Cria RetornoAPI de sucesso padrão do Datasul.
     * 
     * @param mensagem Mensagem de sucesso
     * @return RetornoAPI configurado para sucesso Datasul
     */
    public static RetornoAPI criarSucessoDatasul(String mensagem) {
        return criarRetornoDatasul(DATASUL_SUCESSO, mensagem);
    }
    
    /**
     * Cria RetornoAPI de erro padrão do Datasul.
     * 
     * @param mensagem Mensagem de erro
     * @return RetornoAPI configurado para erro Datasul
     */
    public static RetornoAPI criarErroDatasul(String mensagem) {
        return criarRetornoDatasul(DATASUL_ERRO_GENERICO, mensagem);
    }
    
    /**
     * Acesso ao RetornoAPI original (para casos especiais).
     * 
     * @return Instância original de RetornoAPI
     */
    public RetornoAPI getRetornoOriginal() {
        return retornoAPI;
    }
    
    /**
     * Verifica se o código indica necessidade de retry (específico Datasul).
     * Alguns códigos do Datasul indicam problemas temporários.
     *
     * @return true se deve tentar novamente
     */
    public boolean deveReitentar() {
        int codigo = retornoAPI.getCodigo();
        // Códigos que indicam problemas temporários no Datasul
        return codigo == DATASUL_ERRO_CONEXAO || codigo == -4 || codigo == -5;
    }

    /**
     * Retorna prioridade do erro para logging (específico Datasul).
     *
     * @return Nível de prioridade (1=baixa, 5=crítica)
     */
    public int getPrioridadeErro() {
        if (isSuccessoDatasul()) {
            return 1; // Sucesso - prioridade baixa
        }

        switch (getTipoErro()) {
            case CONEXAO:
                return 5; // Crítico
            case VALIDACAO:
                return 3; // Médio
            case GENERICO:
            default:
                return 4; // Alto
        }
    }

    /**
     * Converte para formato compatível com APIs legadas do Datasul.
     * Retorna array [codigo, mensagem] como esperado por sistemas antigos.
     *
     * @return Array com código e mensagem formatados para Datasul
     */
    public String[] toFormatoLegadoDatasul() {
        return new String[]{
            getCodigoFormatado(),
            String.format("[DATASUL] %s", retornoAPI.getMensagem())
        };
    }

    /**
     * Cria adapter a partir de RetornoAPI (método factory).
     *
     * @param retornoAPI Instância de RetornoAPI
     * @return Novo adapter ou null se retornoAPI for null
     */
    public static DatasulRetornoAdapter of(RetornoAPI retornoAPI) {
        return retornoAPI != null ? new DatasulRetornoAdapter(retornoAPI) : null;
    }

    /**
     * Cria adapter com validação específica do Datasul.
     * Aplica validações adicionais específicas do contexto Datasul.
     *
     * @param retornoAPI Instância de RetornoAPI
     * @return Adapter validado para Datasul
     * @throws IllegalStateException se o retorno não é válido para Datasul
     */
    public static DatasulRetornoAdapter ofValidated(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            throw new IllegalStateException("RetornoAPI é obrigatório para operações Datasul");
        }

        // Validações específicas do Datasul
        if (retornoAPI.getMensagem() == null || retornoAPI.getMensagem().trim().isEmpty()) {
            LOGGER.warning("RetornoAPI sem mensagem - pode causar problemas no Datasul");
        }

        return new DatasulRetornoAdapter(retornoAPI);
    }

    @Override
    public String toString() {
        return getMensagemFormatadaParaLog();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DatasulRetornoAdapter that = (DatasulRetornoAdapter) obj;
        return retornoAPI.equals(that.retornoAPI);
    }

    @Override
    public int hashCode() {
        return retornoAPI.hashCode();
    }

    /**
     * Enum para tipos de erro específicos do Datasul.
     */
    public enum TipoErroDatasul {
        NENHUM("Nenhum erro"),
        GENERICO("Erro genérico"),
        VALIDACAO("Erro de validação"),
        CONEXAO("Erro de conexão");

        private final String descricao;

        TipoErroDatasul(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }
    }
}

@echo off
echo ========================================
echo Recarregando Workspace VSCode
echo ========================================

echo.
echo 1. Limpando cache do Java Language Server...
if exist "%USERPROFILE%\.vscode\extensions\redhat.java-*\server\workspaces" (
    rmdir /s /q "%USERPROFILE%\.vscode\extensions\redhat.java-*\server\workspaces" 2>nul
    echo Cache do Java Language Server limpo.
) else (
    echo Cache do Java Language Server nao encontrado.
)

echo.
echo 2. Limpando workspace metadata...
if exist ".metadata" (
    rmdir /s /q ".metadata" 2>nul
    echo Metadata do workspace limpo.
)

echo.
echo 3. Verificando configuracoes do VSCode...
if exist ".vscode\settings.json" (
    echo Configuracoes encontradas em .vscode\settings.json
) else (
    echo AVISO: Arquivo .vscode\settings.json nao encontrado!
)

echo.
echo 4. Verificando dependencias do Windchill...
set WINDCHILL_HOME=D:\Windchill
if exist "%WINDCHILL_HOME%\lib\esi.jar" (
    echo ✓ esi.jar encontrado
) else (
    echo ✗ esi.jar NAO encontrado em %WINDCHILL_HOME%\lib\
)

if exist "%WINDCHILL_HOME%\lib\wnc.jar" (
    echo ✓ wnc.jar encontrado
) else (
    echo ✗ wnc.jar NAO encontrado em %WINDCHILL_HOME%\lib\
)

if exist "%WINDCHILL_HOME%\codebase\WEB-INF\lib\ieWeb.jar" (
    echo ✓ ieWeb.jar encontrado
) else (
    echo ✗ ieWeb.jar NAO encontrado em %WINDCHILL_HOME%\codebase\WEB-INF\lib\
)

echo.
echo 5. Recarregando VSCode...
echo Pressione Ctrl+Shift+P no VSCode e execute:
echo "Java: Reload Projects"
echo.
echo Ou feche e reabra o VSCode para aplicar as mudancas.
echo.

pause

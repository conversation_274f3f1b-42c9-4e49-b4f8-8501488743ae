@echo off
echo ========================================
echo  Setup Maven para Projeto integracaoDatasul
echo ========================================
echo.

REM Definir caminhos
set PROJETO_ATUAL=D:\Windchill\src\com\infoaxis\integracaoDatasul
set PROJETO_MAVEN=D:\Projetos\Java\integracaoDatasul-maven

echo Verificando estrutura atual...
if not exist "%PROJETO_ATUAL%" (
    echo ERRO: Projeto atual não encontrado em %PROJETO_ATUAL%
    pause
    exit /b 1
)

echo Projeto atual encontrado: %PROJETO_ATUAL%
echo.

REM Criar estrutura Maven
echo Criando estrutura Maven em: %PROJETO_MAVEN%
mkdir "%PROJETO_MAVEN%" 2>nul
mkdir "%PROJETO_MAVEN%\src\main\java\com\infoaxis\integracaoDatasul" 2>nul
mkdir "%PROJETO_MAVEN%\src\main\resources" 2>nul
mkdir "%PROJETO_MAVEN%\src\test\java" 2>nul
mkdir "%PROJETO_MAVEN%\src\test\resources" 2>nul
mkdir "%PROJETO_MAVEN%\.vscode" 2>nul

echo Estrutura Maven criada!
echo.

REM Copiar código atual (SEM MOVER - apenas cópia)
echo Copiando código atual (mantendo original intacto)...
xcopy "%PROJETO_ATUAL%\core" "%PROJETO_MAVEN%\src\main\java\com\infoaxis\integracaoDatasul\core\" /E /I /Q
xcopy "%PROJETO_ATUAL%\esi" "%PROJETO_MAVEN%\src\main\java\com\infoaxis\integracaoDatasul\esi\" /E /I /Q
xcopy "%PROJETO_ATUAL%\*.java" "%PROJETO_MAVEN%\src\main\java\com\infoaxis\integracaoDatasul\" /Q

REM Copiar configurações VSCode
if exist "%PROJETO_ATUAL%\.vscode" (
    xcopy "%PROJETO_ATUAL%\.vscode" "%PROJETO_MAVEN%\.vscode\" /E /I /Q
    echo Configurações VSCode copiadas
)

echo.
echo ========================================
echo  Setup Concluído!
echo ========================================
echo.
echo Projeto Maven criado em: %PROJETO_MAVEN%
echo Código original mantido em: %PROJETO_ATUAL%
echo.
echo Próximos passos:
echo 1. Verificar se tudo foi copiado corretamente
echo 2. Configurar pom.xml
echo 3. Abrir no VSCode
echo.

pause

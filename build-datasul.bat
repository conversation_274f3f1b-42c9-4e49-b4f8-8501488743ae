@echo off
echo ========================================
echo  Build Projeto integracaoDatasul
echo ========================================
echo.

REM Navegar para o projeto Maven
set PROJETO_MAVEN=D:\Projetos\Java\integracaoDatasul-maven

if not exist "%PROJETO_MAVEN%" (
    echo ERRO: Projeto Maven não encontrado!
    echo Execute primeiro: setup-maven-datasul.bat
    pause
    exit /b 1
)

cd /d "%PROJETO_MAVEN%"

echo Projeto: %PROJETO_MAVEN%
echo.

REM Verificar se Maven está instalado
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo AVISO: Maven não encontrado no PATH!
    echo Tentando compilar com javac...
    echo.
    
    REM Compilação manual com javac
    echo Compilando com javac...
    javac -cp "D:\Windchill\lib\*;D:\Windchill\codebase\WEB-INF\lib\*;D:\eclipse\cust_Windchill_src;D:\Windchill\src\com\infoaxis\core;D:\Windchill\src\com\infoaxis\esi" -d "D:\Windchill\codebase" src\main\java\com\infoaxis\integracaoDatasul\**\*.java
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo  COMPILAÇÃO MANUAL CONCLUÍDA!
        echo ========================================
        echo.
        echo Classes geradas em: D:\Windchill\codebase\com\infoaxis\integracaoDatasul\
        echo.
        echo Para testar:
        echo 1. Reinicie o servidor Windchill
        echo 2. Teste suas funcionalidades
        echo.
    ) else (
        echo ERRO: Falha na compilação manual!
        echo Verifique os erros acima.
    )
) else (
    echo Maven encontrado! Executando build Maven...
    echo.
    
    REM Build com Maven
    call mvn clean compile
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo  BUILD MAVEN CONCLUÍDO!
        echo ========================================
        echo.
        echo Classes geradas em: D:\Windchill\codebase\com\infoaxis\integracaoDatasul\
        echo.
        echo Para testar:
        echo 1. Reinicie o servidor Windchill
        echo 2. Teste suas funcionalidades
        echo.
    ) else (
        echo ERRO: Falha no build Maven!
        echo Verifique os erros acima.
    )
)

echo.
echo Estrutura do projeto:
echo - Código fonte: %PROJETO_MAVEN%\src\main\java\
echo - Classes compiladas: D:\Windchill\codebase\com\infoaxis\integracaoDatasul\
echo - Código original: D:\Windchill\src\com\infoaxis\integracaoDatasul\
echo.

pause

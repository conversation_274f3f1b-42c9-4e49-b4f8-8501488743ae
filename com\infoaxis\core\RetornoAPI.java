/** Classe auxiliar para geração e mensagens de retorno na API
 * Infoaxis - Desenvolvimento
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 29/10/2024 | 1.0    | <PERSON><PERSON><PERSON>  | Criação da classe.
 * 11/07/2025 | 2.0    | F<PERSON><PERSON>  | Melhorias: validação defensiva, construtores alternativos, compatibilidade com RetornoMensagem.
 */
package com.infoaxis.core;

public class RetornoAPI {
    private int codigo;
    private String mensagem;

    /**
     * Construtor principal com código numérico.
     * 
     * @param codigo Código de retorno (numérico)
     * @param mensagem Mensagem descritiva
     */
    public RetornoAPI(int codigo, String mensagem) {
        this.codigo = codigo;
        this.mensagem = mensagem != null ? mensagem : "";
    }

    /**
     * Construtor alternativo com código string (compatibilidade com RetornoMensagem).
     * Converte automaticamente string para int quando possível.
     * 
     * @param codigoStr Código de retorno (string)
     * @param mensagem Mensagem descritiva
     */
    public RetornoAPI(String codigoStr, String mensagem) {
        this.codigo = parseCodigoSafely(codigoStr);
        this.mensagem = mensagem != null ? mensagem : "";
    }

    /**
     * Converte código string para int de forma segura.
     * 
     * @param codigoStr Código em formato string
     * @return Código convertido para int (-1 se inválido)
     */
    private static int parseCodigoSafely(String codigoStr) {
        if (codigoStr == null || codigoStr.trim().isEmpty()) {
            return -1;
        }
        try {
            return Integer.parseInt(codigoStr.trim());
        } catch (NumberFormatException e) {
            // Se não conseguir converter, retorna -1 (erro)
            return -1;
        }
    }

    public int getCodigo() {
        return codigo;
    }

    /**
     * Retorna código como string (compatibilidade com RetornoMensagem).
     * 
     * @return Código convertido para string
     */
    public String getCodigoAsString() {
        return String.valueOf(codigo);
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    /**
     * Define código a partir de string (compatibilidade com RetornoMensagem).
     * 
     * @param codigoStr Código em formato string
     */
    public void setCodigo(String codigoStr) {
        this.codigo = parseCodigoSafely(codigoStr);
    }

    public String getMensagem() {
        // Validação defensiva similar à RetornoMensagem
        if (mensagem == null || mensagem.isEmpty()) {
            return "";
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem != null ? mensagem : "";
    }

    /**
     * Retorna resultado formatado.
     * 
     * @param formato Formato personalizado (opcional)
     * @return String formatada com código e mensagem
     */
    public String getResultado(String formato) {
        if (formato == null || formato.isEmpty())
            return codigo + " | " + getMensagem();
        return String.format(formato, codigo, getMensagem());
    }

    /**
     * Verifica se o retorno indica sucesso.
     * 
     * @return true se código indica sucesso (>= 0)
     */
    public boolean isSucesso() {
        return codigo >= 0;
    }

    /**
     * Verifica se o retorno indica erro.
     * 
     * @return true se código indica erro (< 0)
     */
    public boolean isErro() {
        return codigo < 0;
    }

    @Override
    public String toString() {
        // ToString limpo similar ao RetornoMensagem para logs mais limpos
        return "{ " +
                "codigo='" + codigo + '\'' +
                ", mensagem='" + getMensagem() + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        RetornoAPI that = (RetornoAPI) obj;
        return codigo == that.codigo && 
               getMensagem().equals(that.getMensagem());
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(codigo, getMensagem());
    }

    // ========== MÉTODOS DE MIGRAÇÃO ==========
    
    /**
     * Cria RetornoAPI a partir de RetornoMensagem (para migração).
     * 
     * @param retornoMensagem Instância de RetornoMensagem
     * @return Nova instância de RetornoAPI
     */
    public static RetornoAPI fromRetornoMensagem(Object retornoMensagem) {
        if (retornoMensagem == null) {
            return new RetornoAPI(-1, "Retorno nulo");
        }
        
        try {
            // Usa reflection para acessar os campos da RetornoMensagem
            java.lang.reflect.Method getCodigoMethod = retornoMensagem.getClass().getMethod("getCodigo");
            java.lang.reflect.Method getMensagemMethod = retornoMensagem.getClass().getMethod("getMensagem");
            
            String codigo = (String) getCodigoMethod.invoke(retornoMensagem);
            String mensagem = (String) getMensagemMethod.invoke(retornoMensagem);
            
            return new RetornoAPI(codigo, mensagem);
            
        } catch (Exception e) {
            return new RetornoAPI(-1, "Erro na conversão: " + e.getMessage());
        }
    }

    /**
     * Converte esta instância para formato compatível com RetornoMensagem.
     * 
     * @return Array com [codigo, mensagem] em formato string
     */
    public String[] toRetornoMensagemFormat() {
        return new String[]{getCodigoAsString(), getMensagem()};
    }
}

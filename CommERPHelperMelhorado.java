package com.infoaxis.integracaoDatasul.esi;

import java.util.logging.Logger;
import java.util.logging.Level;
import java.net.SocketTimeoutException;
import java.net.ConnectException;
import java.sql.SQLException;

/**
 * Versão melhorada da classe CommERPHelper com boas práticas aplicadas.
 * 
 * Melhorias implementadas:
 * - Validações defensivas robustas
 * - Logging estruturado
 * - Tratamento de casos extremos
 * - Mensagens de erro padronizadas
 * - Categorização automática de erros
 * 
 * <AUTHOR>
 * @version 2.0
 */
public final class CommERPHelperMelhorado {
    
    private static final Logger LOGGER = Logger.getLogger(CommERPHelperMelhorado.class.getName());
    
    // Códigos de erro padronizados
    private static final String CONNECTION_TIMEOUT = "ERR_CONN_TIMEOUT";
    private static final String CONNECTION_REFUSED = "ERR_CONN_REFUSED";
    private static final String DATABASE_ERROR = "ERR_DATABASE";
    private static final String AUTHENTICATION_ERROR = "ERR_AUTH";
    private static final String VALIDATION_ERROR = "ERR_VALIDATION";
    private static final String UNKNOWN_ERROR = "ERR_UNKNOWN";
    
    /**
     * Construtor privado para classe utilitária.
     */
    private CommERPHelperMelhorado() {
        throw new UnsupportedOperationException("Classe utilitária não deve ser instanciada");
    }
    
    /**
     * Versão melhorada do método trataErroGenerico com validações defensivas.
     * 
     * @param endPoint Endpoint onde ocorreu o erro (não pode ser null/vazio)
     * @param exception Exceção capturada (não pode ser null)
     * @return Mensagem de erro formatada e categorizada
     */
    public static String trataErroGenerico(String endPoint, Exception exception) {
        // Validações defensivas
        if (endPoint == null || endPoint.trim().isEmpty()) {
            endPoint = "ENDPOINT_DESCONHECIDO";
            LOGGER.warning("Método trataErroGenerico chamado com endpoint null/vazio");
        }
        
        if (exception == null) {
            String errorMsg = "Método trataErroGenerico chamado com exceção null para endpoint: " + endPoint;
            LOGGER.severe(errorMsg);
            return formatErrorMessage(UNKNOWN_ERROR, endPoint, "Erro desconhecido - exceção null", null);
        }
        
        try {
            // Log do erro para auditoria
            logErrorForAudit(endPoint, exception);
            
            // Categoriza e formata o erro
            return categorizeAndFormatError(endPoint, exception);
            
        } catch (Exception handlingException) {
            // Fallback em caso de erro no próprio tratamento
            LOGGER.log(Level.SEVERE, "Erro no tratamento de exceção para endpoint: " + endPoint, handlingException);
            return formatErrorMessage(UNKNOWN_ERROR, endPoint, 
                "Erro interno no tratamento de exceções", handlingException);
        }
    }
    
    /**
     * Categoriza e formata o erro baseado no tipo de exceção.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção a ser categorizada
     * @return Mensagem formatada
     */
    private static String categorizeAndFormatError(String endPoint, Exception exception) {
        String errorCode;
        String userMessage;
        
        // Categorização baseada no tipo de exceção
        if (exception instanceof SocketTimeoutException) {
            errorCode = CONNECTION_TIMEOUT;
            userMessage = "Timeout na conexão com o sistema ERP";
        } else if (exception instanceof ConnectException) {
            errorCode = CONNECTION_REFUSED;
            userMessage = "Conexão recusada pelo sistema ERP";
        } else if (exception instanceof SQLException) {
            errorCode = DATABASE_ERROR;
            userMessage = "Erro de acesso ao banco de dados";
        } else if (exception instanceof SecurityException) {
            errorCode = AUTHENTICATION_ERROR;
            userMessage = "Erro de autenticação/autorização";
        } else if (exception instanceof IllegalArgumentException) {
            errorCode = VALIDATION_ERROR;
            userMessage = "Dados inválidos fornecidos";
        } else {
            errorCode = UNKNOWN_ERROR;
            userMessage = "Erro inesperado na integração";
        }
        
        return formatErrorMessage(errorCode, endPoint, userMessage, exception);
    }
    
    /**
     * Formata mensagem de erro de forma consistente.
     * 
     * @param errorCode Código do erro
     * @param endPoint Endpoint onde ocorreu
     * @param userMessage Mensagem amigável ao usuário
     * @param exception Exceção original (pode ser null)
     * @return Mensagem formatada
     */
    private static String formatErrorMessage(String errorCode, String endPoint, 
                                           String userMessage, Exception exception) {
        StringBuilder message = new StringBuilder();
        message.append("[").append(errorCode).append("] ");
        message.append("Endpoint: ").append(endPoint).append(" - ");
        message.append(userMessage);
        
        if (exception != null && exception.getMessage() != null) {
            message.append(" (Detalhes: ").append(exception.getMessage()).append(")");
        }
        
        return message.toString();
    }
    
    /**
     * Registra erro para auditoria com nível apropriado.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção original
     */
    private static void logErrorForAudit(String endPoint, Exception exception) {
        if (LOGGER.isLoggable(Level.SEVERE)) {
            LOGGER.log(Level.SEVERE, 
                String.format("Erro de integração ERP - Endpoint: %s, Tipo: %s, Mensagem: %s", 
                    endPoint, 
                    exception.getClass().getSimpleName(), 
                    exception.getMessage()), 
                exception);
        }
    }
    
    /**
     * Verifica se uma exceção é recuperável (pode ser tentada novamente).
     * 
     * @param exception Exceção a ser verificada
     * @return true se for recuperável
     */
    public static boolean isRecoverableError(Exception exception) {
        return exception instanceof SocketTimeoutException ||
               exception instanceof ConnectException ||
               (exception instanceof SQLException && 
                exception.getMessage() != null && 
                exception.getMessage().contains("timeout"));
    }
    
    /**
     * Cria mensagem de erro estruturada para logging.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção original
     * @param additionalInfo Informações adicionais
     * @return Mensagem estruturada
     */
    public static String createStructuredErrorMessage(String endPoint, Exception exception, String additionalInfo) {
        StringBuilder structured = new StringBuilder();
        structured.append("ERRO_INTEGRACAO_ERP|");
        structured.append("ENDPOINT=").append(endPoint != null ? endPoint : "UNKNOWN").append("|");
        structured.append("TIPO=").append(exception != null ? exception.getClass().getSimpleName() : "UNKNOWN").append("|");
        structured.append("MENSAGEM=").append(exception != null && exception.getMessage() != null ? exception.getMessage() : "N/A").append("|");
        structured.append("RECUPERAVEL=").append(exception != null ? isRecoverableError(exception) : false).append("|");
        
        if (additionalInfo != null && !additionalInfo.trim().isEmpty()) {
            structured.append("INFO_ADICIONAL=").append(additionalInfo);
        }
        
        return structured.toString();
    }
    
    /**
     * Método utilitário para validar parâmetros de entrada.
     * 
     * @param value Valor a ser validado
     * @param paramName Nome do parâmetro
     * @throws IllegalArgumentException se valor inválido
     */
    public static void validateNotEmpty(String value, String paramName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Parâmetro '" + paramName + "' não pode ser null ou vazio");
        }
    }
    
    /**
     * Método utilitário para validar objetos não nulos.
     * 
     * @param object Objeto a ser validado
     * @param paramName Nome do parâmetro
     * @throws IllegalArgumentException se objeto for null
     */
    public static void validateNotNull(Object object, String paramName) {
        if (object == null) {
            throw new IllegalArgumentException("Parâmetro '" + paramName + "' não pode ser null");
        }
    }
}

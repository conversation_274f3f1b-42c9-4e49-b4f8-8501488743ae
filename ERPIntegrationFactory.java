package com.infoaxis.integracaoDatasul.esi;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Factory para criação de estratégias de integração ERP.
 * Facilita a adição de novos ERPs sem modificar código existente.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class ERPIntegrationFactory {
    
    private static final Logger LOGGER = Logger.getLogger(ERPIntegrationFactory.class.getName());
    
    // Cache thread-safe de estratégias para reutilização
    private static final Map<ERPIntegrationStrategy.ERPType, ERPIntegrationStrategy> strategyCache = 
        new ConcurrentHashMap<>();
    
    /**
     * Construtor privado para classe utilitária.
     */
    private ERPIntegrationFactory() {
        throw new UnsupportedOperationException("Classe utilitária não deve ser instanciada");
    }
    
    /**
     * Cria ou retorna estratégia cached para o tipo de ERP especificado.
     * 
     * @param erpType Tipo do ERP
     * @return Estratégia de integração
     * @throws Exception se tipo não suportado
     */
    public static ERPIntegrationStrategy createStrategy(ERPIntegrationStrategy.ERPType erpType) 
            throws Exception {
        
        CommERPHelperMelhorado.validateNotNull(erpType, "erpType");
        
        // Verifica cache primeiro para performance
        ERPIntegrationStrategy cachedStrategy = strategyCache.get(erpType);
        if (cachedStrategy != null) {
            LOGGER.fine("Retornando estratégia cached para: " + erpType);
            return cachedStrategy;
        }
        
        // Cria nova estratégia se não estiver em cache
        ERPIntegrationStrategy strategy = createNewStrategy(erpType);
        
        // Adiciona ao cache para reutilização
        strategyCache.put(erpType, strategy);
        LOGGER.info("Nova estratégia criada e cached para: " + erpType);
        
        return strategy;
    }
    
    /**
     * Cria estratégia baseada em string (útil para configurações).
     * 
     * @param erpTypeName Nome do tipo de ERP
     * @return Estratégia de integração
     * @throws Exception se tipo não suportado
     */
    public static ERPIntegrationStrategy createStrategy(String erpTypeName) throws Exception {
        ERPIntegrationStrategy.ERPType erpType = ERPIntegrationStrategy.ERPType.fromName(erpTypeName);
        if (erpType == null) {
            throw new Exception("Tipo de ERP não reconhecido: " + erpTypeName);
        }
        
        return createStrategy(erpType);
    }
    
    /**
     * Cria nova instância de estratégia baseada no tipo.
     * 
     * @param erpType Tipo do ERP
     * @return Nova estratégia
     * @throws Exception se tipo não suportado
     */
    private static ERPIntegrationStrategy createNewStrategy(ERPIntegrationStrategy.ERPType erpType) 
            throws Exception {
        
        try {
            switch (erpType) {
                case DATASUL:
                    return new DatasulIntegrationStrategy();
                    
                case SENIOR:
                    return new SeniorIntegrationStrategy();
                    
                case PROTHEUS:
                    return new ProtheusIntegrationStrategy();
                    
                default:
                    throw new Exception("Tipo de ERP não suportado: " + erpType);
            }
        } catch (Exception e) {
            String errorMessage = CommERPHelperMelhorado.trataErroGenerico("factory", e);
            throw new Exception("Erro ao criar estratégia para " + erpType + ": " + errorMessage, e);
        }
    }
    
    /**
     * Limpa cache de estratégias.
     * Útil para testes ou reconfiguração em runtime.
     */
    public static void clearCache() {
        strategyCache.clear();
        LOGGER.info("Cache de estratégias limpo");
    }
    
    /**
     * Retorna tipos de ERP suportados pela factory.
     * 
     * @return Array com tipos suportados
     */
    public static ERPIntegrationStrategy.ERPType[] getSupportedERPTypes() {
        return new ERPIntegrationStrategy.ERPType[]{
            ERPIntegrationStrategy.ERPType.DATASUL, 
            ERPIntegrationStrategy.ERPType.SENIOR, 
            ERPIntegrationStrategy.ERPType.PROTHEUS
        };
    }
    
    /**
     * Verifica se um tipo de ERP é suportado.
     * 
     * @param erpType Tipo a verificar
     * @return true se suportado
     */
    public static boolean isSupported(ERPIntegrationStrategy.ERPType erpType) {
        if (erpType == null) return false;
        
        for (ERPIntegrationStrategy.ERPType supported : getSupportedERPTypes()) {
            if (supported == erpType) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Verifica se um tipo de ERP (string) é suportado.
     * 
     * @param erpTypeName Nome do tipo a verificar
     * @return true se suportado
     */
    public static boolean isSupported(String erpTypeName) {
        ERPIntegrationStrategy.ERPType erpType = ERPIntegrationStrategy.ERPType.fromName(erpTypeName);
        return isSupported(erpType);
    }
    
    /**
     * Retorna estatísticas do cache para monitoramento.
     * 
     * @return String com estatísticas
     */
    public static String getCacheStats() {
        return String.format("Cache de estratégias: %d entradas ativas", 
                           strategyCache.size());
    }
}

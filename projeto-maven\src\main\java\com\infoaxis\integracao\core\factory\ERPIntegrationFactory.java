package com.infoaxis.integracao.core.factory;

import com.infoaxis.integracao.core.service.ERPIntegrationStrategy;
import com.infoaxis.integracao.core.service.ERPIntegrationStrategy.ERPType;
import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.erp.datasul.DatasulIntegrationStrategy;
import com.infoaxis.integracao.erp.senior.SeniorIntegrationStrategy;
import com.infoaxis.integracao.erp.protheus.ProtheusIntegrationStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Factory para criação de estratégias de integração ERP.
 * Implementa padrão Factory com cache de instâncias para performance.
 * Facilita extensibilidade para novos ERPs.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public final class ERPIntegrationFactory {
    
    private static final Logger LOGGER = Logger.getLogger(ERPIntegrationFactory.class.getName());
    
    // Cache thread-safe de estratégias para reutilização
    private static final Map<ERPType, ERPIntegrationStrategy> strategyCache = 
        new ConcurrentHashMap<>();
    
    /**
     * Construtor privado para classe utilitária.
     */
    private ERPIntegrationFactory() {
        throw new UnsupportedOperationException("Classe utilitária não deve ser instanciada");
    }
    
    /**
     * Cria ou retorna estratégia cached para o tipo de ERP especificado.
     * 
     * @param erpType Tipo do ERP
     * @return Estratégia de integração
     * @throws ERPIntegrationException se tipo não suportado
     */
    public static ERPIntegrationStrategy createStrategy(ERPType erpType) 
            throws ERPIntegrationException {
        
        if (erpType == null) {
            throw new ERPIntegrationException("FACTORY_ERROR", "factory", 
                "Tipo de ERP não pode ser nulo", null);
        }
        
        // Verifica cache primeiro para performance
        ERPIntegrationStrategy cachedStrategy = strategyCache.get(erpType);
        if (cachedStrategy != null) {
            LOGGER.fine("Retornando estratégia cached para: " + erpType);
            return cachedStrategy;
        }
        
        // Cria nova estratégia se não estiver em cache
        ERPIntegrationStrategy strategy = createNewStrategy(erpType);
        
        // Adiciona ao cache para reutilização
        strategyCache.put(erpType, strategy);
        LOGGER.info("Nova estratégia criada e cached para: " + erpType);
        
        return strategy;
    }
    
    /**
     * Cria estratégia baseada em string (útil para configurações).
     * 
     * @param erpTypeName Nome do tipo de ERP
     * @return Estratégia de integração
     * @throws ERPIntegrationException se tipo não suportado
     */
    public static ERPIntegrationStrategy createStrategy(String erpTypeName) 
            throws ERPIntegrationException {
        
        ERPType erpType = ERPType.fromName(erpTypeName);
        if (erpType == null) {
            throw new ERPIntegrationException("FACTORY_ERROR", "factory", 
                "Tipo de ERP não reconhecido: " + erpTypeName, null);
        }
        
        return createStrategy(erpType);
    }
    
    /**
     * Cria nova instância de estratégia baseada no tipo.
     * 
     * @param erpType Tipo do ERP
     * @return Nova estratégia
     * @throws ERPIntegrationException se tipo não suportado
     */
    private static ERPIntegrationStrategy createNewStrategy(ERPType erpType) 
            throws ERPIntegrationException {
        
        try {
            switch (erpType) {
                case DATASUL:
                    return createDatasulStrategy();
                    
                case SENIOR:
                    return createSeniorStrategy();
                    
                case PROTHEUS:
                    return createProtheusStrategy();
                    
                default:
                    throw new ERPIntegrationException("UNSUPPORTED_ERP", "factory", 
                        "Tipo de ERP não suportado: " + erpType, null);
            }
        } catch (Exception e) {
            throw new ERPIntegrationException("FACTORY_ERROR", "factory", 
                "Erro ao criar estratégia para " + erpType, e);
        }
    }
    
    /**
     * Cria estratégia específica para Datasul.
     * Centraliza configuração e dependências.
     * 
     * @return Estratégia Datasul configurada
     */
    private static ERPIntegrationStrategy createDatasulStrategy() {
        // TODO: Implementar injeção de dependências adequada
        // Por enquanto, criação simples - pode ser melhorada com IoC container
        return new DatasulIntegrationStrategy();
    }
    
    /**
     * Cria estratégia específica para Senior.
     * 
     * @return Estratégia Senior configurada
     */
    private static ERPIntegrationStrategy createSeniorStrategy() {
        return new SeniorIntegrationStrategy();
    }
    
    /**
     * Cria estratégia específica para Protheus.
     * 
     * @return Estratégia Protheus configurada
     */
    private static ERPIntegrationStrategy createProtheusStrategy() {
        return new ProtheusIntegrationStrategy();
    }
    
    /**
     * Limpa cache de estratégias.
     * Útil para testes ou reconfiguração em runtime.
     */
    public static void clearCache() {
        strategyCache.clear();
        LOGGER.info("Cache de estratégias limpo");
    }
    
    /**
     * Retorna tipos de ERP suportados pela factory.
     * 
     * @return Array com tipos suportados
     */
    public static ERPType[] getSupportedERPTypes() {
        return new ERPType[]{ERPType.DATASUL, ERPType.SENIOR, ERPType.PROTHEUS};
    }
    
    /**
     * Verifica se um tipo de ERP é suportado.
     * 
     * @param erpType Tipo a verificar
     * @return true se suportado
     */
    public static boolean isSupported(ERPType erpType) {
        if (erpType == null) return false;
        
        for (ERPType supported : getSupportedERPTypes()) {
            if (supported == erpType) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Verifica se um tipo de ERP (string) é suportado.
     * 
     * @param erpTypeName Nome do tipo a verificar
     * @return true se suportado
     */
    public static boolean isSupported(String erpTypeName) {
        ERPType erpType = ERPType.fromName(erpTypeName);
        return isSupported(erpType);
    }
    
    /**
     * Retorna estatísticas do cache para monitoramento.
     * 
     * @return String com estatísticas
     */
    public static String getCacheStats() {
        return String.format("Cache de estratégias: %d entradas ativas", 
                           strategyCache.size());
    }
}

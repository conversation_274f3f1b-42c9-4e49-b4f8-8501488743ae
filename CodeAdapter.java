/** Adaptador para conversões seguras entre tipos de código (String ↔ int).
 * Implementa o padrão Adapter para resolver incompatibilidades de tipo
 * causadas pela migração RetornoMensagem → RetornoAPI.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | Fábio M Valente  | Criação do adaptador para compatibilidade de tipos.
 */
package com.infoaxis.core;

import com.infoaxis.integracaoDatasul.core.UtilDatasul;

public class CodeAdapter {
    
    /**
     * Converte código de RetornoAPI para formato String esperado pelo código legado.
     * Aplica formatação Datasul quando necessário.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param aplicarFormatacao Se deve aplicar formatação específica do Datasul
     * @return Código formatado como String
     */
    public static String getCodigoAsString(RetornoAPI retornoAPI, boolean aplicarFormatacao) {
        if (retornoAPI == null) {
            return "-1";
        }
        
        String codigo = retornoAPI.getCodigoAsString();
        
        if (aplicarFormatacao) {
            return UtilDatasul.formataCodigoDatasul(codigo, true);
        }
        
        return codigo;
    }
    
    /**
     * Converte código de RetornoAPI para formato String (sem formatação).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como String
     */
    public static String getCodigoAsString(RetornoAPI retornoAPI) {
        return getCodigoAsString(retornoAPI, false);
    }
    
    /**
     * Converte código de RetornoAPI para Integer, aplicando validações Datasul.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param aplicarValidacao Se deve aplicar validação específica do Datasul
     * @return Código como Integer
     */
    public static Integer getCodigoAsInteger(RetornoAPI retornoAPI, boolean aplicarValidacao) {
        if (retornoAPI == null) {
            return -1;
        }
        
        if (aplicarValidacao) {
            String codigoFormatado = UtilDatasul.formataCodigoDatasul(retornoAPI.getCodigoAsString(), true);
            return Integer.parseInt(codigoFormatado);
        }
        
        return retornoAPI.getCodigo();
    }
    
    /**
     * Converte código de RetornoAPI para Integer (sem validação).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como Integer
     */
    public static Integer getCodigoAsInteger(RetornoAPI retornoAPI) {
        return getCodigoAsInteger(retornoAPI, false);
    }
    
    /**
     * Método de conveniência para substituir o padrão antigo:
     * Integer.parseInt(UtilDatasul.formataCodigoDatasul(addItem.get().getCodigo(), true))
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código formatado e convertido para Integer
     */
    public static Integer parseCodigoDatasul(RetornoAPI retornoAPI) {
        return getCodigoAsInteger(retornoAPI, true);
    }
    
    /**
     * Verifica se o código indica sucesso (> 0).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return true se código indica sucesso
     */
    public static boolean isSucesso(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            return false;
        }
        return retornoAPI.getCodigo() > 0;
    }
    
    /**
     * Verifica se o código indica erro (<= 0).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return true se código indica erro
     */
    public static boolean isErro(RetornoAPI retornoAPI) {
        return !isSucesso(retornoAPI);
    }
    
    /**
     * Método de conveniência para comparações de código.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param valorComparacao Valor para comparar
     * @return true se códigos são iguais
     */
    public static boolean codigoEquals(RetornoAPI retornoAPI, int valorComparacao) {
        if (retornoAPI == null) {
            return valorComparacao == -1;
        }
        return retornoAPI.getCodigo() == valorComparacao;
    }
    
    /**
     * Método de conveniência para comparações de código com String.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @param valorComparacao Valor String para comparar
     * @return true se códigos são iguais
     */
    public static boolean codigoEquals(RetornoAPI retornoAPI, String valorComparacao) {
        if (retornoAPI == null || valorComparacao == null) {
            return false;
        }
        return retornoAPI.getCodigoAsString().equals(valorComparacao);
    }
    
    // ========== MÉTODOS PARA MIGRAÇÃO GRADUAL ==========
    
    /**
     * Substitui o padrão: addItem.get().getCodigo() quando era String.
     * Use este método durante a migração.
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Código como String (compatibilidade)
     * @deprecated Use getCodigoAsString() diretamente
     */
    @Deprecated
    public static String getCodigo(RetornoAPI retornoAPI) {
        return getCodigoAsString(retornoAPI);
    }
}

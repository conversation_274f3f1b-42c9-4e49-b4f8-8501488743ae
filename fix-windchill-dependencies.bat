@echo off
echo ========================================
echo  Corrigindo Dependencias Windchill
echo ========================================
echo.

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven
cd /d "%PROJETO%"

echo Projeto: %PROJETO%
echo.

echo 1. Verificando JARs do Windchill...
if exist "D:\Windchill\lib" (
    echo [OK] D:\Windchill\lib encontrado
    dir "D:\Windchill\lib\*.jar" /b | find /c ".jar"
) else (
    echo [ERRO] D:\Windchill\lib NAO encontrado!
    pause
    exit /b 1
)

echo.
echo 2. Criando .classpath com TODOS os JARs do Windchill...

echo ^<?xml version="1.0" encoding="UTF-8"?^> > .classpath
echo ^<classpath^> >> .classpath

REM Source folders
echo     ^<classpathentry kind="src" output="target/classes" path="src/main/java"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="optional" value="true"/^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath

echo     ^<classpathentry kind="src" output="target/test-classes" path="src/test/java"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="optional" value="true"/^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath

REM JRE Container
echo     ^<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath

REM Maven Dependencies
echo     ^<classpathentry kind="con" path="org.eclipse.m2e.MAVEN_DEPENDENCY_CLASSPATH"^> >> .classpath
echo         ^<attributes^> >> .classpath
echo             ^<attribute name="maven.pomderived" value="true"/^> >> .classpath
echo         ^</attributes^> >> .classpath
echo     ^</classpathentry^> >> .classpath

REM Principais JARs do Windchill
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/wt.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/esi.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/servlet.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/wnc.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/log4j.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/lib/commons-logging.jar"/^> >> .classpath

REM JARs do WEB-INF
echo     ^<classpathentry kind="lib" path="D:/Windchill/codebase/WEB-INF/lib/ieWeb.jar"/^> >> .classpath
echo     ^<classpathentry kind="lib" path="D:/Windchill/codebase/WEB-INF/lib/wncWeb.jar"/^> >> .classpath

REM Suas classes InfoAxis
echo     ^<classpathentry kind="src" path="D:/Windchill/src/com/infoaxis/core"/^> >> .classpath
echo     ^<classpathentry kind="src" path="D:/Windchill/src/com/infoaxis/esi"/^> >> .classpath

REM Bibliotecas de desenvolvimento
echo     ^<classpathentry kind="lib" path="D:/eclipse/cust_Windchill_src"/^> >> .classpath

REM Output
echo     ^<classpathentry kind="output" path="target/classes"/^> >> .classpath
echo ^</classpath^> >> .classpath

echo.
echo 3. Atualizando settings.json do VSCode...

echo { > .vscode\settings.json
echo     "java.configuration.updateBuildConfiguration": "automatic", >> .vscode\settings.json
echo     "java.compile.nullAnalysis.mode": "automatic", >> .vscode\settings.json
echo     "java.dependency.packagePresentation": "hierarchical", >> .vscode\settings.json
echo     "java.dependency.syncWithFolderExplorer": true, >> .vscode\settings.json
echo     "maven.executable.path": "mvn", >> .vscode\settings.json
echo     "maven.terminal.useJavaHome": true, >> .vscode\settings.json
echo     "java.project.sourcePaths": [ >> .vscode\settings.json
echo         "src/main/java", >> .vscode\settings.json
echo         "src/test/java", >> .vscode\settings.json
echo         "D:/Windchill/src/com/infoaxis/core", >> .vscode\settings.json
echo         "D:/Windchill/src/com/infoaxis/esi" >> .vscode\settings.json
echo     ], >> .vscode\settings.json
echo     "java.project.referencedLibraries": [ >> .vscode\settings.json
echo         "D:/Windchill/lib/wt.jar", >> .vscode\settings.json
echo         "D:/Windchill/lib/esi.jar", >> .vscode\settings.json
echo         "D:/Windchill/lib/servlet.jar", >> .vscode\settings.json
echo         "D:/Windchill/lib/wnc.jar", >> .vscode\settings.json
echo         "D:/Windchill/lib/log4j.jar", >> .vscode\settings.json
echo         "D:/Windchill/lib/commons-logging.jar", >> .vscode\settings.json
echo         "D:/Windchill/codebase/WEB-INF/lib/ieWeb.jar", >> .vscode\settings.json
echo         "D:/Windchill/codebase/WEB-INF/lib/wncWeb.jar", >> .vscode\settings.json
echo         "D:/eclipse/cust_Windchill_src/**/*.jar" >> .vscode\settings.json
echo     ], >> .vscode\settings.json
echo     "files.encoding": "utf8", >> .vscode\settings.json
echo     "java.project.encoding": "UTF-8", >> .vscode\settings.json
echo     "java.format.onSave.enabled": true, >> .vscode\settings.json
echo     "editor.formatOnSave": true, >> .vscode\settings.json
echo     "java.completion.enabled": true, >> .vscode\settings.json
echo     "java.completion.guessMethodArguments": true, >> .vscode\settings.json
echo     "java.maxConcurrentBuilds": 1 >> .vscode\settings.json
echo } >> .vscode\settings.json

echo.
echo 4. Verificando JARs principais...
if exist "D:\Windchill\lib\wt.jar" (
    echo [OK] wt.jar encontrado - Classes wt.* disponiveis
) else (
    echo [AVISO] wt.jar NAO encontrado!
)

if exist "D:\Windchill\lib\esi.jar" (
    echo [OK] esi.jar encontrado
) else (
    echo [AVISO] esi.jar NAO encontrado!
)

echo.
echo ========================================
echo  DEPENDENCIAS CORRIGIDAS!
echo ========================================
echo.
echo Na janela do projeto Maven:
echo 1. Pressione Ctrl+Shift+P
echo 2. Digite: "Java: Reload Projects"
echo 3. Aguarde processar (pode demorar 2-3 minutos)
echo 4. Teste abrir CommERPHelper.java novamente
echo.
echo Agora deve funcionar:
echo [✓] wt.util.WTException reconhecido
echo [✓] Imports clicaveis
echo [✓] Autocomplete das classes Windchill
echo.

pause

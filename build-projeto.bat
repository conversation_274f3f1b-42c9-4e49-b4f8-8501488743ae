@echo off
echo ========================================
echo  Build do Projeto Windchill ERP Integration
echo ========================================
echo.

REM Navegar para o diretório do projeto
cd /d "D:\Projetos\Java\windchill-erp-integration"

echo Verificando estrutura do projeto...
if not exist "src\main\java" (
    echo ERRO: Estrutura Maven não encontrada!
    pause
    exit /b 1
)

echo Estrutura OK!
echo.

REM Verificar se Maven está instalado
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo AVISO: Maven não encontrado no PATH!
    echo Tentando compilar com javac...
    echo.
    
    REM Compilação manual com javac
    echo Compilando com javac...
    javac -cp "D:\Windchill\lib\*;D:\Windchill\codebase\WEB-INF\lib\*" -d "D:\Windchill\codebase" src\main\java\com\infoaxis\integracaoDatasul\esi\*.java
    
    if %ERRORLEVEL% EQU 0 (
        echo Compilação manual concluída com sucesso!
        echo Classes geradas em: D:\Windchill\codebase\com\infoaxis\integracaoDatasul\esi\
    ) else (
        echo ERRO: Falha na compilação manual!
    )
) else (
    echo Maven encontrado! Executando build Maven...
    echo.
    
    REM Build com Maven
    call mvn clean compile
    
    if %ERRORLEVEL% EQU 0 (
        echo Build Maven concluído com sucesso!
        echo Classes geradas em: D:\Windchill\codebase\com\infoaxis\
    ) else (
        echo ERRO: Falha no build Maven!
    )
)

echo.
echo ========================================
echo  Build Finalizado
echo ========================================
echo.
echo Para testar as melhorias:
echo 1. Reinicie o servidor Windchill
echo 2. Use CommERPHelperMelhorado.trataErroGenerico() no seu código
echo 3. Teste o Strategy Pattern com ERPIntegrationFactory.createStrategy()
echo.

pause

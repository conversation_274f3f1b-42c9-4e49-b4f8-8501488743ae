package com.infoaxis.integracao.core.exception;

/**
 * Exceção específica para problemas de conexão com sistemas ERP.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ConnectionException extends ERPIntegrationException {
    
    private static final long serialVersionUID = 1L;
    
    private final int timeoutSeconds;
    private final String serverUrl;
    
    /**
     * Construtor para erros de conexão.
     * 
     * @param endPoint Endpoint que falhou
     * @param serverUrl URL do servidor
     * @param timeoutSeconds Timeout configurado
     * @param cause Causa raiz
     */
    public ConnectionException(String endPoint, String serverUrl, int timeoutSeconds, Throwable cause) {
        super("CONNECTION_ERROR", endPoint, 
              String.format("Falha na conexão com %s (timeout: %ds)", serverUrl, timeoutSeconds), 
              cause, serverUrl, timeoutSeconds);
        this.timeoutSeconds = timeoutSeconds;
        this.serverUrl = serverUrl;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
}

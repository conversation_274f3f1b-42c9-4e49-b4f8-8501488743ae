package com.infoaxis.integracao.core.util;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import static org.junit.jupiter.api.Assertions.*;
import java.net.SocketTimeoutException;
import java.sql.SQLException;

/**
 * Testes unitários para ERPCommunicationHelper.
 * Demonstra como testar as melhorias implementadas.
 * 
 * <AUTHOR>
 * @version 1.0
 */
class ERPCommunicationHelperTest {
    
    @Nested
    @DisplayName("Testes do método processIntegrationError")
    class ProcessIntegrationErrorTests {
        
        @Test
        @DisplayName("Deve processar erro com parâmetros válidos")
        void shouldProcessErrorWithValidParameters() {
            // Arrange
            String endpoint = "datasul/items";
            Exception exception = new RuntimeException("Erro de teste");
            
            // Act
            String result = ERPCommunicationHelper.processIntegrationError(endpoint, exception);
            
            // Assert
            assertNotNull(result);
            assertTrue(result.contains(endpoint));
            assertTrue(result.contains("ERR_GENERIC"));
        }
        
        @Test
        @DisplayName("Deve lançar exceção para endpoint null")
        void shouldThrowExceptionForNullEndpoint() {
            // Arrange
            Exception exception = new RuntimeException("Erro de teste");
            
            // Act & Assert
            IllegalArgumentException thrown = assertThrows(
                IllegalArgumentException.class,
                () -> ERPCommunicationHelper.processIntegrationError(null, exception)
            );
            
            assertTrue(thrown.getMessage().contains("endPoint"));
        }
        
        @Test
        @DisplayName("Deve lançar exceção para endpoint vazio")
        void shouldThrowExceptionForEmptyEndpoint() {
            // Arrange
            Exception exception = new RuntimeException("Erro de teste");
            
            // Act & Assert
            IllegalArgumentException thrown = assertThrows(
                IllegalArgumentException.class,
                () -> ERPCommunicationHelper.processIntegrationError("", exception)
            );
            
            assertTrue(thrown.getMessage().contains("endPoint"));
        }
        
        @Test
        @DisplayName("Deve lançar exceção para exception null")
        void shouldThrowExceptionForNullException() {
            // Arrange
            String endpoint = "test/endpoint";
            
            // Act & Assert
            IllegalArgumentException thrown = assertThrows(
                IllegalArgumentException.class,
                () -> ERPCommunicationHelper.processIntegrationError(endpoint, null)
            );
            
            assertTrue(thrown.getMessage().contains("exception"));
        }
        
        @Test
        @DisplayName("Deve categorizar SocketTimeoutException corretamente")
        void shouldCategorizeTimeoutException() {
            // Arrange
            String endpoint = "datasul/timeout";
            Exception exception = new SocketTimeoutException("Connection timeout");
            
            // Act
            String result = ERPCommunicationHelper.processIntegrationError(endpoint, exception);
            
            // Assert
            assertTrue(result.contains("ERR_CONN_TIMEOUT"));
            assertTrue(result.contains("timeout"));
        }
        
        @Test
        @DisplayName("Deve categorizar SQLException corretamente")
        void shouldCategorizeSQLException() {
            // Arrange
            String endpoint = "database/query";
            Exception exception = new SQLException("Database connection failed");
            
            // Act
            String result = ERPCommunicationHelper.processIntegrationError(endpoint, exception);
            
            // Assert
            assertTrue(result.contains("ERR_DATABASE"));
            assertTrue(result.contains("banco de dados"));
        }
    }
    
    @Nested
    @DisplayName("Testes do método createIntegrationException")
    class CreateIntegrationExceptionTests {
        
        @Test
        @DisplayName("Deve criar ERPIntegrationException com dados corretos")
        void shouldCreateIntegrationExceptionWithCorrectData() {
            // Arrange
            String endpoint = "senior/products";
            Exception originalException = new RuntimeException("Erro original");
            
            // Act
            ERPIntegrationException result = ERPCommunicationHelper
                .createIntegrationException(endpoint, originalException);
            
            // Assert
            assertNotNull(result);
            assertEquals(endpoint, result.getEndPoint());
            assertEquals("ERR_GENERIC", result.getErrorCode());
            assertEquals(originalException, result.getCause());
        }
        
        @Test
        @DisplayName("Deve determinar código de erro para timeout")
        void shouldDetermineTimeoutErrorCode() {
            // Arrange
            String endpoint = "protheus/sync";
            Exception timeoutException = new SocketTimeoutException("Timeout occurred");
            
            // Act
            ERPIntegrationException result = ERPCommunicationHelper
                .createIntegrationException(endpoint, timeoutException);
            
            // Assert
            assertEquals("ERR_TIMEOUT", result.getErrorCode());
        }
    }
    
    @Nested
    @DisplayName("Testes de compatibilidade com método legado")
    class LegacyCompatibilityTests {
        
        @Test
        @DisplayName("Método deprecated deve funcionar corretamente")
        void shouldWorkWithDeprecatedMethod() {
            // Arrange
            String endpoint = "legacy/endpoint";
            Exception exception = new RuntimeException("Legacy error");
            
            // Act
            @SuppressWarnings("deprecation")
            String result = ERPCommunicationHelper.trataErroGenerico(endpoint, exception);
            
            // Assert
            assertNotNull(result);
            assertTrue(result.contains(endpoint));
        }
    }
    
    @Nested
    @DisplayName("Testes de casos extremos")
    class EdgeCaseTests {
        
        @Test
        @DisplayName("Deve tratar endpoint com espaços")
        void shouldHandleEndpointWithSpaces() {
            // Arrange
            String endpoint = "  valid/endpoint  ";
            Exception exception = new RuntimeException("Test error");
            
            // Act & Assert
            assertDoesNotThrow(() -> 
                ERPCommunicationHelper.processIntegrationError(endpoint, exception)
            );
        }
        
        @Test
        @DisplayName("Deve tratar exceção com mensagem null")
        void shouldHandleExceptionWithNullMessage() {
            // Arrange
            String endpoint = "test/endpoint";
            Exception exception = new RuntimeException((String) null);
            
            // Act
            String result = ERPCommunicationHelper.processIntegrationError(endpoint, exception);
            
            // Assert
            assertNotNull(result);
            assertTrue(result.contains(endpoint));
        }
        
        @Test
        @DisplayName("Deve tratar exceção aninhada")
        void shouldHandleNestedException() {
            // Arrange
            String endpoint = "nested/error";
            Exception rootCause = new SQLException("Database error");
            Exception wrappedException = new RuntimeException("Wrapper error", rootCause);
            
            // Act
            String result = ERPCommunicationHelper.processIntegrationError(endpoint, wrappedException);
            
            // Assert
            assertNotNull(result);
            assertTrue(result.contains(endpoint));
        }
    }
}

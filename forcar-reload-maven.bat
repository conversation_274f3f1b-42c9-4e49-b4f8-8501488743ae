@echo off
echo ========================================
echo Forçando Reload do Projeto Maven
echo ========================================

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven

echo.
echo 1. Limpando cache do Java Language Server...
for /d %%i in ("%USERPROFILE%\.vscode\extensions\redhat.java-*") do (
    if exist "%%i\server\workspaces" (
        echo Limpando: %%i\server\workspaces
        rmdir /s /q "%%i\server\workspaces" 2>nul
    )
)

echo.
echo 2. Limpando target do Maven...
if exist "%PROJETO%\target" (
    rmdir /s /q "%PROJETO%\target" 2>nul
    echo ✓ Target limpo
)

echo.
echo 3. Verificando se VSCode está rodando...
tasklist /FI "IMAGENAME eq Code.exe" 2>nul | find /I "Code.exe" >nul
if %ERRORLEVEL%==0 (
    echo VSCode está rodando. Fechando...
    taskkill /F /IM Code.exe 2>nul
    timeout /t 3 /nobreak >nul
)

echo.
echo 4. Verificando configurações atualizadas...
if exist "%PROJETO%\pom.xml" (
    echo ✓ pom.xml atualizado
) else (
    echo ✗ pom.xml não encontrado
)

if exist "%PROJETO%\.vscode\settings.json" (
    echo ✓ settings.json atualizado
) else (
    echo ✗ settings.json não encontrado
)

echo.
echo 5. Abrindo VSCode com projeto limpo...
cd /d "%PROJETO%"
start "" code .

echo.
echo ========================================
echo PROJETO RECARREGADO!
echo ========================================
echo.
echo AGUARDE o VSCode carregar completamente:
echo.
echo 1. Aguarde Maven resolver dependências
echo 2. Aguarde Java Language Server inicializar
echo 3. Abra um arquivo .java
echo 4. Se ainda houver erros, execute no VSCode:
echo    - Ctrl+Shift+P
echo    - "Java: Reload Projects"
echo    - "Maven: Reload Projects"
echo    - "Developer: Reload Window"
echo.
echo IMPORTANTE: As classes com.infoaxis.core.* agora
echo devem ser encontradas através do codebase do Windchill
echo em D:/Windchill/src
echo.
echo ========================================

pause

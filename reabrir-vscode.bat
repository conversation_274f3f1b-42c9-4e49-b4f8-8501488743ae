@echo off
echo ========================================
echo  Reabrindo VSCode com Configuracao Java
echo ========================================
echo.

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven

echo Fechando VSCode atual...
taskkill /f /im Code.exe 2>nul

echo Aguardando 3 segundos...
timeout /t 3 /nobreak >nul

echo Abrindo VSCode no projeto Maven...
code "%PROJETO%"

echo.
echo ========================================
echo  VSCode Reaberto!
echo ========================================
echo.
echo AGUARDE 1-2 MINUTOS para o VSCode processar o projeto.
echo.
echo Voce deve ver:
echo [✓] "Java Projects" na barra lateral esquerda
echo [✓] Icone do Maven na barra lateral
echo [✓] Imports clicaveis (em azul)
echo [✓] Autocomplete funcionando
echo.
echo Se nao funcionar:
echo 1. Pressione Ctrl+Shift+P
echo 2. Digite: "Java: Reload Projects"
echo 3. Aguarde processar
echo.
echo Para testar:
echo 1. Abra: src\main\java\com\infoaxis\integracaoDatasul\esi\CommERPHelper.java
echo 2. Clique em algum import para ver se navega
echo 3. Digite "System." para ver autocomplete
echo.

pause

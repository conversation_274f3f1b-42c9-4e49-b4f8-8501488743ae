# Estrutura do Projeto Maven - Windchill ERP Integration

## 📁 Estrutura de Diretórios Proposta

```
D:\Projetos\Java\windchill-erp-integration\
├── pom.xml                                    # Configuração Maven
├── README.md                                  # Documentação do projeto
├── .gitignore                                # Arquivos ignorados pelo Git
├── .vscode/                                  # Configurações VSCode
│   ├── settings.json                         # Configurações do workspace
│   ├── launch.json                           # Configurações de debug
│   └── tasks.json                            # Tasks de build
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── infoaxis/
│   │   │           └── integracao/
│   │   │               ├── core/             # Funcionalidades comuns
│   │   │               │   ├── exception/    # Exceções customizadas
│   │   │               │   ├── util/         # Utilitários
│   │   │               │   ├── config/       # Configurações
│   │   │               │   ├── service/      # Serviços base
│   │   │               │   └── model/        # Modelos de dados
│   │   │               ├── erp/              # Implementações específicas
│   │   │               │   ├── datasul/      # Datasul/TOTVS
│   │   │               │   ├── senior/       # Senior
│   │   │               │   └── protheus/     # Protheus
│   │   │               └── integration/      # Camada de integração
│   │   │                   ├── api/          # APIs
│   │   │                   └── connector/    # Conectores
│   │   └── resources/
│   │       ├── logback.xml                   # Configuração de logging
│   │       ├── application.properties        # Propriedades da aplicação
│   │       └── META-INF/
│   │           └── services/                 # Service providers
│   └── test/
│       ├── java/
│       │   └── com/
│       │       └── infoaxis/
│       │           └── integracao/
│       │               ├── core/             # Testes das funcionalidades core
│       │               ├── erp/              # Testes específicos por ERP
│       │               └── integration/      # Testes de integração
│       └── resources/
│           ├── logback-test.xml              # Logging para testes
│           └── test-data/                    # Dados de teste
├── docs/                                     # Documentação
│   ├── api/                                  # Documentação da API
│   ├── setup/                                # Guias de instalação
│   └── examples/                             # Exemplos de uso
├── scripts/                                  # Scripts utilitários
│   ├── build.bat                             # Script de build Windows
│   ├── deploy.bat                            # Script de deploy
│   └── setup-dev.bat                         # Setup ambiente dev
└── target/                                   # Diretório de build (gerado)
    ├── classes/                              # Classes compiladas
    ├── test-classes/                         # Classes de teste
    └── windchill-erp-integration-1.0.0.jar  # JAR final
```

## 🎯 Benefícios da Nova Estrutura

### **1. Separação Clara de Responsabilidades**
- **src/main/java**: Código fonte principal
- **src/test/java**: Testes unitários e integração
- **src/main/resources**: Configurações e recursos
- **docs/**: Documentação separada do código

### **2. Organização por Domínio**
- **core/**: Funcionalidades compartilhadas
- **erp/**: Implementações específicas por ERP
- **integration/**: Camada de integração com Windchill

### **3. Configuração Profissional**
- Maven para gerenciamento de dependências
- Profiles para diferentes ambientes
- Build automatizado
- Testes integrados

### **4. Integração com Windchill**
- Compilação direta para `D:\Windchill\codebase`
- Dependências do Windchill configuradas
- Estrutura compatível com deployment

## 🔧 Configurações Especiais

### **Output Directory**
```xml
<outputDirectory>${windchill.codebase}</outputDirectory>
```
- Classes compiladas vão direto para o codebase do Windchill
- Windchill reconhece automaticamente as mudanças

### **System Dependencies**
```xml
<dependency>
    <groupId>com.ptc.windchill</groupId>
    <artifactId>esi</artifactId>
    <scope>system</scope>
    <systemPath>${windchill.lib}/esi.jar</systemPath>
</dependency>
```
- Referencia JARs do Windchill sem copiá-los
- Mantém compatibilidade com versões específicas

### **Clean Plugin Customizado**
```xml
<fileset>
    <directory>${windchill.codebase}/com/infoaxis</directory>
    <includes>
        <include>**/*.class</include>
    </includes>
</fileset>
```
- Remove apenas classes do seu projeto
- Preserva outras classes do Windchill

## 🚀 Comandos Maven Úteis

### **Desenvolvimento**
```bash
mvn clean compile          # Compila e coloca no codebase
mvn test                   # Executa testes
mvn clean test             # Limpa e testa
```

### **Build Completo**
```bash
mvn clean package          # Build completo com JAR
mvn clean install          # Instala no repositório local
```

### **Profiles**
```bash
mvn clean compile -Pdev    # Profile desenvolvimento
mvn clean package -Pprod   # Profile produção
```

## 📋 Próximos Passos

1. **Criar estrutura de diretórios**
2. **Configurar VSCode**
3. **Migrar código existente**
4. **Configurar Git**
5. **Implementar melhorias**

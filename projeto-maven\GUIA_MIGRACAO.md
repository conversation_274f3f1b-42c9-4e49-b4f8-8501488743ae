# 🚀 Guia de Migração - Windchill ERP Integration

## 📋 Pré-requisitos

Antes de iniciar a migração, certifique-se de ter:

- ✅ **Java 17+** instalado e configurado
- ✅ **Maven 3.8+** instalado e no PATH
- ✅ **VSCode** com extensões Java
- ✅ **Windchill** funcionando em `D:\Windchill`
- ✅ **Backup** do código atual

## 🎯 Passo a Passo da Migração

### **Passo 1: Executar Setup Automático**

```bash
# Navegar para o diretório dos scripts
cd projeto-maven/scripts

# Executar setup automático
setup-dev.bat
```

Este script irá:
- ✅ Verificar pré-requisitos
- ✅ Criar estrutura de diretórios em `D:\Projetos\Java\windchill-erp-integration`
- ✅ Copiar arquivos de configuração
- ✅ Configurar ambiente de desenvolvimento

### **Passo 2: Abrir Projeto no VSCode**

```bash
# Abrir VSCode no novo projeto
code "D:\Projetos\Java\windchill-erp-integration"
```

O VSCode irá:
- 🔄 Detectar automaticamente o projeto Maven
- 🔄 Baixar dependências
- 🔄 Configurar classpath com bibliotecas do Windchill
- 🔄 Ativar IntelliSense

### **Passo 3: Migrar Código Existente**

#### **3.1 Localizar Código Atual**
```bash
# Seu código atual está em:
D:\Windchill\src\com\infoaxis\
```

#### **3.2 Copiar para Nova Estrutura**
```bash
# Copiar arquivos Java para:
D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\
```

#### **3.3 Organizar por Domínio**

**Estrutura Recomendada:**
```
src/main/java/com/infoaxis/integracao/
├── core/                    # Funcionalidades comuns
│   ├── exception/           # Suas exceções customizadas
│   ├── util/               # CommERPHelper e outros utilitários
│   ├── config/             # Configurações
│   └── service/            # Serviços base
├── erp/                    # Implementações específicas
│   ├── datasul/            # Classes específicas Datasul
│   ├── senior/             # Classes específicas Senior
│   └── protheus/           # Classes específicas Protheus (futuro)
└── integration/            # Camada de integração
    ├── api/                # APIs de integração
    └── connector/          # Conectores específicos
```

### **Passo 4: Ajustar Imports e Pacotes**

#### **4.1 Atualizar Declarações de Pacote**

**Antes:**
```java
package com.infoaxis.integracaoDatasul.esi;
```

**Depois:**
```java
package com.infoaxis.integracao.erp.datasul;
```

#### **4.2 Atualizar Imports**

**Antes:**
```java
import com.infoaxis.integracaoDatasul.esi.CommERPHelper;
```

**Depois:**
```java
import com.infoaxis.integracao.core.util.ERPCommunicationHelper;
```

### **Passo 5: Primeiro Build**

```bash
# Navegar para o projeto
cd "D:\Projetos\Java\windchill-erp-integration"

# Executar primeiro build
scripts\build.bat
```

**O que acontece:**
- 🔄 Maven compila o código
- 🔄 Classes são geradas em `D:\Windchill\codebase\com\infoaxis`
- 🔄 Windchill reconhece automaticamente as mudanças

### **Passo 6: Testar Integração**

#### **6.1 Verificar Classes Compiladas**
```bash
# Verificar se classes foram geradas
dir "D:\Windchill\codebase\com\infoaxis" /s
```

#### **6.2 Testar no Windchill**
1. Reiniciar servidor Windchill
2. Testar funcionalidades existentes
3. Verificar logs para erros

### **Passo 7: Configurar Git (Opcional)**

```bash
# Inicializar repositório
git init

# Adicionar arquivos
git add .

# Primeiro commit
git commit -m "Migração inicial para estrutura Maven"
```

## 🔧 Comandos Úteis

### **Desenvolvimento Diário**
```bash
# Build rápido
mvn compile

# Build com testes
mvn clean test

# Deploy completo
scripts\deploy.bat
```

### **Debugging**
```bash
# Build com debug
mvn compile -X

# Limpar cache Maven
mvn dependency:purge-local-repository
```

### **VSCode Tasks**
- `Ctrl+Shift+P` → "Tasks: Run Task"
- Selecionar "Build and Deploy to Windchill"

## 🚨 Troubleshooting

### **Problema: Maven não encontra dependências do Windchill**
**Solução:**
```bash
# Verificar se Windchill está em D:\Windchill
# Verificar se variável WINDCHILL_HOME está configurada
```

### **Problema: Classes não aparecem no codebase**
**Solução:**
```bash
# Verificar outputDirectory no pom.xml
# Executar mvn clean compile
# Verificar permissões de escrita
```

### **Problema: VSCode não reconhece projeto Maven**
**Solução:**
1. `Ctrl+Shift+P` → "Java: Reload Projects"
2. Verificar se extensão "Extension Pack for Java" está instalada
3. Reiniciar VSCode

### **Problema: Imports não resolvem**
**Solução:**
```bash
# Recarregar workspace
Ctrl+Shift+P → "Developer: Reload Window"

# Verificar classpath
Ctrl+Shift+P → "Java: Show Build Job Status"
```

## 📈 Próximos Passos

Após migração bem-sucedida:

1. **Implementar Melhorias** (conforme relatório anterior)
2. **Criar Testes Unitários**
3. **Configurar CI/CD**
4. **Documentar APIs**
5. **Treinar Equipe**

## 🎉 Benefícios Alcançados

✅ **Projeto independente** em `D:\Projetos\Java`
✅ **Gerenciamento de dependências** com Maven
✅ **Build automatizado** para Windchill
✅ **IntelliSense completo** no VSCode
✅ **Estrutura profissional** e escalável
✅ **Backup automático** durante deploy
✅ **Testes integrados** ao build
✅ **Documentação automática** com JavaDoc

## 📞 Suporte

Em caso de dúvidas:
1. Consulte logs do Maven: `target/maven.log`
2. Verifique configurações VSCode: `.vscode/settings.json`
3. Execute diagnóstico: `mvn help:system`

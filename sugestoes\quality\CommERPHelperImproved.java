package com.infoaxis.integracao.core.util;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.core.util.ErrorHandler;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Versão melhorada da classe CommERPHelper com boas práticas aplicadas.
 * 
 * Melhorias implementadas:
 * - Nomenclatura mais clara e descritiva
 * - Documentação JavaDoc completa
 * - Tratamento de erros robusto
 * - Métodos com responsabilidade única
 * - Validações defensivas
 * - Logging estruturado
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 1.0
 */
public final class ERPCommunicationHelper {
    
    private static final Logger LOGGER = Logger.getLogger(ERPCommunicationHelper.class.getName());
    
    // Constantes para códigos de erro padronizados
    private static final String ERROR_NULL_ENDPOINT = "ERR_NULL_ENDPOINT";
    private static final String ERROR_EMPTY_ENDPOINT = "ERR_EMPTY_ENDPOINT";
    private static final String ERROR_NULL_EXCEPTION = "ERR_NULL_EXCEPTION";
    private static final String ERROR_PROCESSING_FAILED = "ERR_PROCESSING_FAILED";
    
    /**
     * Construtor privado para classe utilitária.
     * Previne instanciação acidental.
     */
    private ERPCommunicationHelper() {
        throw new UnsupportedOperationException("Classe utilitária não deve ser instanciada");
    }
    
    /**
     * Processa e formata erros de integração ERP de forma padronizada.
     * 
     * Versão melhorada do método trataErroGenerico original com:
     * - Validações defensivas robustas
     * - Logging estruturado
     * - Tratamento de casos extremos
     * - Mensagens de erro padronizadas
     * 
     * @param endPoint Identificador do endpoint onde ocorreu o erro (não pode ser null/vazio)
     * @param exception Exceção capturada durante a operação (não pode ser null)
     * @return Mensagem de erro formatada e padronizada
     * @throws IllegalArgumentException se parâmetros inválidos
     * 
     * @since 2.0
     */
    public static String processIntegrationError(String endPoint, Exception exception) {
        // Validações defensivas com mensagens específicas
        validateEndPoint(endPoint);
        validateException(exception);
        
        try {
            // Log do erro para auditoria (nível apropriado)
            logErrorForAudit(endPoint, exception);
            
            // Delega para o handler especializado
            return ErrorHandler.handleIntegrationError(endPoint, exception);
            
        } catch (Exception processingException) {
            // Fallback robusto em caso de falha no processamento
            return handleProcessingFailure(endPoint, exception, processingException);
        }
    }
    
    /**
     * Valida o parâmetro endPoint.
     * 
     * @param endPoint Endpoint a ser validado
     * @throws IllegalArgumentException se inválido
     */
    private static void validateEndPoint(String endPoint) {
        if (endPoint == null) {
            String errorMsg = "Parâmetro 'endPoint' não pode ser null";
            LOGGER.severe(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        
        if (endPoint.trim().isEmpty()) {
            String errorMsg = "Parâmetro 'endPoint' não pode ser vazio";
            LOGGER.severe(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
    }
    
    /**
     * Valida o parâmetro exception.
     * 
     * @param exception Exceção a ser validada
     * @throws IllegalArgumentException se inválida
     */
    private static void validateException(Exception exception) {
        if (exception == null) {
            String errorMsg = "Parâmetro 'exception' não pode ser null";
            LOGGER.severe(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
    }
    
    /**
     * Registra erro para auditoria com nível apropriado.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção original
     */
    private static void logErrorForAudit(String endPoint, Exception exception) {
        if (LOGGER.isLoggable(Level.SEVERE)) {
            LOGGER.log(Level.SEVERE, 
                String.format("Erro de integração ERP - Endpoint: %s, Tipo: %s, Mensagem: %s", 
                    endPoint, 
                    exception.getClass().getSimpleName(), 
                    exception.getMessage()), 
                exception);
        }
    }
    
    /**
     * Trata falhas no próprio processamento de erros.
     * 
     * @param endPoint Endpoint original
     * @param originalException Exceção original
     * @param processingException Exceção durante processamento
     * @return Mensagem de erro de fallback
     */
    private static String handleProcessingFailure(String endPoint, Exception originalException, 
                                                Exception processingException) {
        
        // Log crítico da falha no processamento
        LOGGER.log(Level.SEVERE, 
            "Falha crítica no processamento de erro para endpoint: " + endPoint, 
            processingException);
        
        // Retorna mensagem de fallback simples mas informativa
        return String.format("[%s] Endpoint: %s - Erro crítico no processamento (Original: %s, Processamento: %s)", 
            ERROR_PROCESSING_FAILED,
            endPoint,
            originalException.getClass().getSimpleName(),
            processingException.getClass().getSimpleName());
    }
    
    /**
     * Versão sobrecarregada para compatibilidade com código legado.
     * 
     * @deprecated Use {@link #processIntegrationError(String, Exception)} 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção capturada
     * @return Mensagem de erro formatada
     */
    @Deprecated(since = "2.0", forRemoval = true)
    public static String trataErroGenerico(String endPoint, Exception exception) {
        LOGGER.warning("Método deprecated 'trataErroGenerico' chamado. " +
                      "Use 'processIntegrationError' em seu lugar.");
        return processIntegrationError(endPoint, exception);
    }
    
    /**
     * Cria exceção de integração padronizada a partir de erro genérico.
     * 
     * @param endPoint Endpoint onde ocorreu o erro
     * @param exception Exceção original
     * @return Exceção de integração estruturada
     * @since 2.0
     */
    public static ERPIntegrationException createIntegrationException(String endPoint, Exception exception) {
        validateEndPoint(endPoint);
        validateException(exception);
        
        String errorMessage = processIntegrationError(endPoint, exception);
        
        return new ERPIntegrationException(
            determineErrorCode(exception),
            endPoint,
            errorMessage,
            exception
        );
    }
    
    /**
     * Determina código de erro baseado no tipo de exceção.
     * 
     * @param exception Exceção a ser analisada
     * @return Código de erro apropriado
     */
    private static String determineErrorCode(Exception exception) {
        String exceptionType = exception.getClass().getSimpleName();
        
        if (exceptionType.contains("Timeout")) {
            return "ERR_TIMEOUT";
        } else if (exceptionType.contains("Connection")) {
            return "ERR_CONNECTION";
        } else if (exceptionType.contains("Authentication") || exceptionType.contains("Security")) {
            return "ERR_AUTH";
        } else if (exceptionType.contains("Validation") || exceptionType.contains("IllegalArgument")) {
            return "ERR_VALIDATION";
        } else {
            return "ERR_GENERIC";
        }
    }
}

/** Exemplo de uso do DatasulRetornoAdapter.
 * Demonstra como utilizar o adapter para operações específicas do Datasul.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | <PERSON><PERSON>bio <PERSON>  | Exemplo de uso do adapter Datasul.
 */
package com.infoaxis.integracaoDatasul.examples;

import com.infoaxis.core.RetornoAPI;
import com.infoaxis.integracaoDatasul.adapters.DatasulRetornoAdapter;
import java.util.logging.Logger;

/**
 * Exemplos práticos de uso do DatasulRetornoAdapter.
 * 
 * Esta classe demonstra os principais cenários de uso do adapter
 * em operações típicas do Datasul.
 */
public class ExemploUsoDatasulRetornoAdapter {
    
    private static final Logger LOGGER = Logger.getLogger(ExemploUsoDatasulRetornoAdapter.class.getName());
    
    /**
     * Exemplo 1: Uso básico do adapter.
     */
    public static void exemploUsoBasico() {
        LOGGER.info("=== Exemplo 1: Uso Básico ===");
        
        // Cenário: Retorno de uma operação Datasul
        RetornoAPI retorno = new RetornoAPI(0, "Item criado com sucesso");
        
        // Criar adapter
        DatasulRetornoAdapter adapter = new DatasulRetornoAdapter(retorno);
        
        // Usar funcionalidades específicas do Datasul
        System.out.println("Código formatado: " + adapter.getCodigoFormatado());
        System.out.println("É sucesso? " + adapter.isSuccessoDatasul());
        System.out.println("Log formatado: " + adapter.getMensagemFormatadaParaLog());
    }
    
    /**
     * Exemplo 2: Tratamento de erros específicos do Datasul.
     */
    public static void exemploTratamentoErros() {
        LOGGER.info("=== Exemplo 2: Tratamento de Erros ===");
        
        // Cenário: Erro de validação do Datasul
        RetornoAPI retornoErro = new RetornoAPI(-2, "Código do item inválido");
        DatasulRetornoAdapter adapterErro = DatasulRetornoAdapter.of(retornoErro);
        
        if (adapterErro.isErroDatasul()) {
            System.out.println("Tipo de erro: " + adapterErro.getTipoErro().getDescricao());
            System.out.println("Prioridade: " + adapterErro.getPrioridadeErro());
            System.out.println("Deve reinitentar? " + adapterErro.deveReitentar());
            
            // Log contextualizado
            LOGGER.warning(adapterErro.getMensagemFormatadaParaLog());
        }
    }
    
    /**
     * Exemplo 3: Integração com código legado.
     */
    public static void exemploIntegracaoLegado() {
        LOGGER.info("=== Exemplo 3: Integração com Código Legado ===");
        
        RetornoAPI retorno = new RetornoAPI(0, "Operação concluída");
        DatasulRetornoAdapter adapter = new DatasulRetornoAdapter(retorno);
        
        // Para sistemas legados que esperam array [codigo, mensagem]
        String[] formatoLegado = adapter.toFormatoLegadoDatasul();
        System.out.println("Formato legado - Código: " + formatoLegado[0]);
        System.out.println("Formato legado - Mensagem: " + formatoLegado[1]);
    }
    
    /**
     * Exemplo 4: Criação de retornos padronizados para Datasul.
     */
    public static void exemploCriacaoRetornos() {
        LOGGER.info("=== Exemplo 4: Criação de Retornos Padronizados ===");
        
        // Criar retorno de sucesso padrão Datasul
        RetornoAPI sucesso = DatasulRetornoAdapter.criarSucessoDatasul("Item processado com sucesso");
        System.out.println("Sucesso: " + sucesso);
        
        // Criar retorno de erro padrão Datasul
        RetornoAPI erro = DatasulRetornoAdapter.criarErroDatasul("Falha na validação dos dados");
        System.out.println("Erro: " + erro);
        
        // Usar adapters nos retornos criados
        DatasulRetornoAdapter adapterSucesso = DatasulRetornoAdapter.of(sucesso);
        DatasulRetornoAdapter adapterErro = DatasulRetornoAdapter.of(erro);
        
        System.out.println("Adapter sucesso: " + adapterSucesso);
        System.out.println("Adapter erro: " + adapterErro);
    }
    
    /**
     * Exemplo 5: Uso em operações típicas do Datasul (simulação).
     */
    public static void exemploOperacoesDatasul() {
        LOGGER.info("=== Exemplo 5: Operações Típicas do Datasul ===");
        
        // Simular retorno de uma operação de criação de item
        RetornoAPI retornoCriacao = simularCriacaoItem("ITEM001", "Parafuso M6");
        DatasulRetornoAdapter adapter = DatasulRetornoAdapter.ofValidated(retornoCriacao);
        
        if (adapter.isSuccessoDatasul()) {
            LOGGER.info("Item criado com sucesso: " + adapter.getCodigoFormatado());
        } else {
            LOGGER.severe("Falha na criação: " + adapter.getMensagemFormatadaParaLog());
            
            if (adapter.deveReitentar()) {
                LOGGER.info("Tentando novamente...");
                // Lógica de retry aqui
            }
        }
    }
    
    /**
     * Simula uma operação de criação de item no Datasul.
     * 
     * @param codigo Código do item
     * @param descricao Descrição do item
     * @return RetornoAPI simulado
     */
    private static RetornoAPI simularCriacaoItem(String codigo, String descricao) {
        // Simulação: 80% de sucesso, 20% de erro
        boolean sucesso = Math.random() > 0.2;
        
        if (sucesso) {
            return DatasulRetornoAdapter.criarSucessoDatasul(
                String.format("Item %s (%s) criado com sucesso", codigo, descricao)
            );
        } else {
            return DatasulRetornoAdapter.criarErroDatasul(
                String.format("Falha ao criar item %s: código já existe", codigo)
            );
        }
    }
    
    /**
     * Executa todos os exemplos.
     */
    public static void main(String[] args) {
        System.out.println("=== EXEMPLOS DE USO: DatasulRetornoAdapter ===\n");
        
        exemploUsoBasico();
        System.out.println();
        
        exemploTratamentoErros();
        System.out.println();
        
        exemploIntegracaoLegado();
        System.out.println();
        
        exemploCriacaoRetornos();
        System.out.println();
        
        exemploOperacoesDatasul();
        
        System.out.println("\n=== FIM DOS EXEMPLOS ===");
    }
}

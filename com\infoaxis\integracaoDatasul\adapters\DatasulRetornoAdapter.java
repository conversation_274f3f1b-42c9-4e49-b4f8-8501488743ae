/** Adaptador para operações específicas do Datasul com RetornoAPI.
 * Implementa o padrão Adapter para encapsular lógicas específicas do Datasul
 * sem contaminar o domínio genérico core.
 * 
 * @Histórico
 * Data       | Versão | Autor            | Descrição
 * -----------|--------|------------------|----------------------------------------
 * 11/07/2025 | 1.0    | Fábio M Valente  | Criação do adaptador específico Datasul.
 */
package com.infoaxis.integracaoDatasul.adapters;

import com.infoaxis.core.RetornoAPI;
import com.infoaxis.integracaoDatasul.core.UtilDatasul;
import java.util.logging.Logger;

/**
 * Adapter para operações específicas do Datasul com RetornoAPI.
 * 
 * Esta classe encapsula toda a lógica específica do Datasul para trabalhar
 * com RetornoAPI, mantendo o domínio core limpo e genérico.
 * 
 * Funcionalidades principais:
 * - Formatação de códigos específica do Datasul
 * - Validação de sucesso baseada em regras Datasul
 * - Conversões e transformações específicas
 * - Logging contextualizado para Datasul
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class DatasulRetornoAdapter {
    
    private static final Logger LOGGER = Logger.getLogger(DatasulRetornoAdapter.class.getName());
    
    // Constantes específicas do Datasul
    private static final int DATASUL_SUCESSO = 0;
    private static final int DATASUL_ERRO_GENERICO = -1;
    private static final int DATASUL_ERRO_VALIDACAO = -2;
    private static final int DATASUL_ERRO_CONEXAO = -3;
    
    private final RetornoAPI retornoAPI;
    
    /**
     * Construtor do adapter.
     * 
     * @param retornoAPI Instância de RetornoAPI a ser adaptada
     * @throws IllegalArgumentException se retornoAPI for null
     */
    public DatasulRetornoAdapter(RetornoAPI retornoAPI) {
        if (retornoAPI == null) {
            throw new IllegalArgumentException("RetornoAPI não pode ser null");
        }
        this.retornoAPI = retornoAPI;
        
        LOGGER.fine("DatasulRetornoAdapter criado para código: " + retornoAPI.getCodigo());
    }
    
    /**
     * Retorna o código formatado segundo padrões do Datasul.
     * Aplica formatação específica para códigos Datasul.
     * 
     * @return Código formatado para Datasul
     */
    public String getCodigoFormatado() {
        String codigo = retornoAPI.getCodigoAsString();
        String formatado = UtilDatasul.formataCodigoDatasul(codigo, true);
        
        LOGGER.fine("Código formatado para Datasul: " + codigo + " -> " + formatado);
        return formatado;
    }
    
    /**
     * Verifica se o retorno indica sucesso segundo regras do Datasul.
     * No Datasul, código 0 = sucesso, qualquer outro valor = erro.
     * 
     * @return true se indica sucesso no contexto Datasul
     */
    public boolean isSuccessoDatasul() {
        boolean sucesso = retornoAPI.getCodigo() == DATASUL_SUCESSO;
        
        LOGGER.fine("Verificação sucesso Datasul - Código: " + retornoAPI.getCodigo() + 
                   ", Sucesso: " + sucesso);
        return sucesso;
    }
    
    /**
     * Verifica se o retorno indica erro segundo regras do Datasul.
     * 
     * @return true se indica erro no contexto Datasul
     */
    public boolean isErroDatasul() {
        return !isSuccessoDatasul();
    }
    
    /**
     * Retorna mensagem formatada para logs do Datasul.
     * Inclui contexto específico do Datasul.
     * 
     * @return Mensagem formatada para logs
     */
    public String getMensagemFormatadaParaLog() {
        String status = isSuccessoDatasul() ? "SUCESSO" : "ERRO";
        
        return String.format("[DATASUL-%s] Código: %s | Mensagem: %s",
                           status,
                           getCodigoFormatado(),
                           retornoAPI.getMensagem());
    }
    
    /**
     * Cria um novo RetornoAPI com formatação específica do Datasul.
     * Útil para padronizar retornos em operações Datasul.
     * 
     * @param codigo Código específico do Datasul
     * @param mensagem Mensagem descritiva
     * @return Nova instância de RetornoAPI formatada para Datasul
     */
    public static RetornoAPI criarRetornoDatasul(int codigo, String mensagem) {
        String mensagemFormatada = String.format("[DATASUL] %s", mensagem != null ? mensagem : "");
        RetornoAPI retorno = new RetornoAPI(codigo, mensagemFormatada);
        
        LOGGER.fine("RetornoAPI criado para Datasul - Código: " + codigo + 
                   ", Mensagem: " + mensagemFormatada);
        return retorno;
    }
    
    /**
     * Cria RetornoAPI de sucesso padrão do Datasul.
     * 
     * @param mensagem Mensagem de sucesso
     * @return RetornoAPI configurado para sucesso Datasul
     */
    public static RetornoAPI criarSucessoDatasul(String mensagem) {
        return criarRetornoDatasul(DATASUL_SUCESSO, mensagem);
    }
    
    /**
     * Cria RetornoAPI de erro padrão do Datasul.
     * 
     * @param mensagem Mensagem de erro
     * @return RetornoAPI configurado para erro Datasul
     */
    public static RetornoAPI criarErroDatasul(String mensagem) {
        return criarRetornoDatasul(DATASUL_ERRO_GENERICO, mensagem);
    }
    
    /**
     * Converte para formato compatível com APIs legadas do Datasul.
     * Retorna array [codigo, mensagem] como esperado por sistemas antigos.
     * 
     * @return Array com código e mensagem formatados para Datasul
     */
    public String[] toFormatoLegadoDatasul() {
        return new String[]{
            getCodigoFormatado(),
            String.format("[DATASUL] %s", retornoAPI.getMensagem())
        };
    }
    
    /**
     * Cria adapter a partir de RetornoAPI (método factory).
     * 
     * @param retornoAPI Instância de RetornoAPI
     * @return Novo adapter ou null se retornoAPI for null
     */
    public static DatasulRetornoAdapter of(RetornoAPI retornoAPI) {
        return retornoAPI != null ? new DatasulRetornoAdapter(retornoAPI) : null;
    }
    
    /**
     * Acesso ao RetornoAPI original (para casos especiais).
     * 
     * @return Instância original de RetornoAPI
     */
    public RetornoAPI getRetornoOriginal() {
        return retornoAPI;
    }
    
    @Override
    public String toString() {
        return getMensagemFormatadaParaLog();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DatasulRetornoAdapter that = (DatasulRetornoAdapter) obj;
        return retornoAPI.equals(that.retornoAPI);
    }
    
    @Override
    public int hashCode() {
        return retornoAPI.hashCode();
    }
}

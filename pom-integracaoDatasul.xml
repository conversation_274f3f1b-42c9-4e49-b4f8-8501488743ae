<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <!-- Informações do Projeto -->
    <groupId>com.infoaxis</groupId>
    <artifactId>integracao-datasul</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    
    <name>Integração Datasul</name>
    <description>Projeto de integração do Windchill com Datasul/TOTVS</description>
    
    <!-- Propriedades -->
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- <PERSON>in<PERSON> do Windchill -->
        <windchill.home>D:/Windchill</windchill.home>
        <windchill.codebase>${windchill.home}/codebase</windchill.codebase>
        <windchill.lib>${windchill.home}/lib</windchill.lib>
        <windchill.webinf.lib>${windchill.codebase}/WEB-INF/lib</windchill.webinf.lib>
        
        <!-- Caminho das suas bibliotecas de desenvolvimento -->
        <dev.libs>D:/eclipse/cust_Windchill_src</dev.libs>
        
        <!-- Caminho das suas classes core e esi -->
        <infoaxis.core>D:/Windchill/src/com/infoaxis/core</infoaxis.core>
        <infoaxis.esi>D:/Windchill/src/com/infoaxis/esi</infoaxis.esi>
    </properties>
    
    <!-- Dependências -->
    <dependencies>
        
        <!-- Dependências do Windchill Core -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>windchill-core</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.codebase}</systemPath>
        </dependency>
        
        <!-- Bibliotecas principais do Windchill -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>esi</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/esi.jar</systemPath>
        </dependency>
        
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>servlet</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/servlet.jar</systemPath>
        </dependency>
        
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>wnc</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/wnc.jar</systemPath>
        </dependency>
        
        <!-- Bibliotecas Web do Windchill -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>ie-web</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.webinf.lib}/ieWeb.jar</systemPath>
        </dependency>
        
        <!-- Suas bibliotecas de desenvolvimento -->
        <dependency>
            <groupId>com.infoaxis</groupId>
            <artifactId>dev-libs</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${dev.libs}</systemPath>
        </dependency>
        
        <!-- Suas classes core da InfoAxis -->
        <dependency>
            <groupId>com.infoaxis</groupId>
            <artifactId>core</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${infoaxis.core}</systemPath>
        </dependency>
        
        <!-- Suas classes esi da InfoAxis -->
        <dependency>
            <groupId>com.infoaxis</groupId>
            <artifactId>esi</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${infoaxis.esi}</systemPath>
        </dependency>
        
        <!-- Dependências externas -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        
        <!-- Dependências de Teste -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>
        
    </dependencies>
    
    <!-- Build Configuration -->
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        
        <!-- Diretório de saída para o Windchill -->
        <outputDirectory>${windchill.codebase}</outputDirectory>
        
        <plugins>
            
            <!-- Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                    <!-- Incluir suas classes no classpath -->
                    <compilerArgs>
                        <arg>-cp</arg>
                        <arg>${infoaxis.core};${infoaxis.esi};${dev.libs}</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            
            <!-- Surefire Plugin para testes -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>
            
            <!-- Clean Plugin customizado -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${windchill.codebase}/com/infoaxis/integracaoDatasul</directory>
                            <includes>
                                <include>**/*.class</include>
                            </includes>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            
        </plugins>
    </build>
    
</project>

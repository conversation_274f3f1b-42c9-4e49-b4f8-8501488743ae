@echo off
echo ========================================
echo  Verificando Setup Java/Maven
echo ========================================
echo.

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven
cd /d "%PROJETO%"

echo Projeto: %PROJETO%
echo.

echo 1. Verificando arquivos criados...
if exist "pom.xml" (
    echo [OK] pom.xml encontrado
) else (
    echo [ERRO] pom.xml NAO encontrado!
)

if exist ".classpath" (
    echo [OK] .classpath encontrado
) else (
    echo [ERRO] .classpath NAO encontrado!
)

if exist ".project" (
    echo [OK] .project encontrado
) else (
    echo [ERRO] .project NAO encontrado!
)

if exist ".vscode\settings.json" (
    echo [OK] .vscode\settings.json encontrado
) else (
    echo [ERRO] .vscode\settings.json NAO encontrado!
)

echo.
echo 2. Verificando estrutura Maven...
if exist "src\main\java" (
    echo [OK] src\main\java encontrado
) else (
    echo [ERRO] src\main\java NAO encontrado!
)

if exist "src\main\java\com\infoaxis\integracaoDatasul" (
    echo [OK] Codigo fonte encontrado
    dir "src\main\java\com\infoaxis\integracaoDatasul" /b
) else (
    echo [ERRO] Codigo fonte NAO encontrado!
)

echo.
echo 3. Verificando dependencias Windchill...
if exist "D:\Windchill\lib\esi.jar" (
    echo [OK] esi.jar encontrado
) else (
    echo [AVISO] esi.jar NAO encontrado em D:\Windchill\lib\
)

if exist "D:\Windchill\src\com\infoaxis\core" (
    echo [OK] core InfoAxis encontrado
) else (
    echo [AVISO] core InfoAxis NAO encontrado
)

if exist "D:\eclipse\cust_Windchill_src" (
    echo [OK] Bibliotecas de desenvolvimento encontradas
) else (
    echo [AVISO] Bibliotecas de desenvolvimento NAO encontradas
)

echo.
echo 4. Testando Java...
java -version
if %ERRORLEVEL% EQU 0 (
    echo [OK] Java funcionando
) else (
    echo [ERRO] Java NAO funcionando!
)

echo.
echo 5. Testando Maven...
mvn -version
if %ERRORLEVEL% EQU 0 (
    echo [OK] Maven funcionando
) else (
    echo [AVISO] Maven nao encontrado (compilacao sera manual)
)

echo.
echo ========================================
echo  INSTRUCOES PARA VSCODE
echo ========================================
echo.
echo 1. FECHE o VSCode completamente
echo 2. REABRA o VSCode no projeto: code "%PROJETO%"
echo 3. AGUARDE 1-2 minutos para processar
echo 4. VERIFIQUE se aparece:
echo    - "Java Projects" na barra lateral esquerda
echo    - Icone Maven na barra lateral
echo    - Imports clicaveis (azuis)
echo.
echo 5. Se NAO funcionar:
echo    - Ctrl+Shift+P
echo    - Digite: "Java: Reload Projects"
echo    - Aguarde processar
echo.
echo 6. Para testar IntelliSense:
echo    - Abra um arquivo .java
echo    - Digite "System." e veja se aparece autocomplete
echo    - Clique em um import para ver se navega
echo.

pause

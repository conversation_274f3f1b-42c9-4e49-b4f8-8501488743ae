package com.infoaxis.integracaoDatasul.esi;

/**
 * Interface Strategy para implementações específicas de cada ERP.
 * Facilita a adição de novos ERPs sem modificar código existente.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ERPIntegrationStrategy {
    
    /**
     * Retorna o tipo de ERP suportado por esta estratégia.
     * 
     * @return Tipo do ERP
     */
    ERPType getSupportedERPType();
    
    /**
     * Valida se a requisição é compatível com este ERP.
     * 
     * @param request Dados da requisição
     * @return true se válida
     * @throws Exception se inválida
     */
    boolean validateRequest(Object request) throws Exception;
    
    /**
     * Executa a integração específica do ERP.
     * 
     * @param request Dados da requisição
     * @return Resultado da integração
     * @throws Exception em caso de erro
     */
    Object executeIntegration(Object request) throws Exception;
    
    /**
     * Testa conectividade com o ERP.
     * 
     * @return true se conectado
     * @throws Exception em caso de falha
     */
    boolean testConnection() throws Exception;
    
    /**
     * Retorna configurações específicas necessárias para este ERP.
     * 
     * @return Array com nomes das configurações obrigatórias
     */
    String[] getRequiredConfigurations();
    
    /**
     * Enum para tipos de ERP suportados.
     */
    enum ERPType {
        DATASUL("Datasul/TOTVS"),
        SENIOR("Senior"),
        PROTHEUS("Protheus"),
        SAP("SAP"),
        ORACLE("Oracle ERP");
        
        private final String displayName;
        
        ERPType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        /**
         * Busca tipo de ERP por nome.
         * 
         * @param name Nome do ERP
         * @return Tipo encontrado ou null
         */
        public static ERPType fromName(String name) {
            if (name == null) return null;
            
            for (ERPType type : values()) {
                if (type.name().equalsIgnoreCase(name) || 
                    type.displayName.equalsIgnoreCase(name)) {
                    return type;
                }
            }
            return null;
        }
    }
}

{"java.configuration.updateBuildConfiguration": "automatic", "java.project.sourcePaths": [".", "D:/Windchill/src"], "java.project.outputPath": "D:/Windchill/codebase", "java.project.referencedLibraries": {"include": ["D:/Windchill/lib/*.jar", "D:/Windchill/codebase/WEB-INF/lib/*.jar", "D:/Windchill/srclib/**/*.jar", "D:/Windchill/thirdparty/**/*.jar"]}, "java.import.maven.enabled": false, "java.import.gradle.enabled": false, "java.compile.nullAnalysis.mode": "automatic"}
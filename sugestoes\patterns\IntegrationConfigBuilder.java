package com.infoaxis.integracao.core.config;

import com.infoaxis.integracao.core.service.ERPIntegrationStrategy.ERPType;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Builder para configurações de integração ERP.
 * Implementa padrão Builder para criação fluente de configurações.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IntegrationConfigBuilder {
    
    private ERPType erpType;
    private String serverUrl;
    private String username;
    private String password;
    private String companyCode;
    private Duration timeout = Duration.ofSeconds(30);
    private int maxRetries = 3;
    private Duration retryDelay = Duration.ofSeconds(5);
    private boolean enableLogging = true;
    private Map<String, Object> customProperties = new HashMap<>();
    
    /**
     * Define o tipo de ERP.
     * 
     * @param erpType Tipo do ERP
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withERPType(ERPType erpType) {
        this.erpType = erpType;
        return this;
    }
    
    /**
     * Define URL do servidor ERP.
     * 
     * @param serverUrl URL do servidor
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
        return this;
    }
    
    /**
     * Define credenciais de acesso.
     * 
     * @param username Nome de usuário
     * @param password Senha
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withCredentials(String username, String password) {
        this.username = username;
        this.password = password;
        return this;
    }
    
    /**
     * Define código da empresa.
     * 
     * @param companyCode Código da empresa
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        return this;
    }
    
    /**
     * Define timeout para operações.
     * 
     * @param timeout Timeout
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withTimeout(Duration timeout) {
        this.timeout = timeout;
        return this;
    }
    
    /**
     * Define configurações de retry.
     * 
     * @param maxRetries Número máximo de tentativas
     * @param retryDelay Delay entre tentativas
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withRetryConfig(int maxRetries, Duration retryDelay) {
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
        return this;
    }
    
    /**
     * Habilita ou desabilita logging.
     * 
     * @param enableLogging true para habilitar
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withLogging(boolean enableLogging) {
        this.enableLogging = enableLogging;
        return this;
    }
    
    /**
     * Adiciona propriedade customizada.
     * 
     * @param key Chave da propriedade
     * @param value Valor da propriedade
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withCustomProperty(String key, Object value) {
        this.customProperties.put(key, value);
        return this;
    }
    
    /**
     * Adiciona múltiplas propriedades customizadas.
     * 
     * @param properties Mapa de propriedades
     * @return Builder para encadeamento
     */
    public IntegrationConfigBuilder withCustomProperties(Map<String, Object> properties) {
        this.customProperties.putAll(properties);
        return this;
    }
    
    /**
     * Constrói a configuração final.
     * 
     * @return Configuração de integração
     * @throws IllegalStateException se configuração inválida
     */
    public IntegrationConfig build() {
        validateConfiguration();
        
        return new IntegrationConfig(
            erpType,
            serverUrl,
            username,
            password,
            companyCode,
            timeout,
            maxRetries,
            retryDelay,
            enableLogging,
            new HashMap<>(customProperties)
        );
    }
    
    /**
     * Valida se a configuração está completa e válida.
     * 
     * @throws IllegalStateException se inválida
     */
    private void validateConfiguration() {
        if (erpType == null) {
            throw new IllegalStateException("Tipo de ERP é obrigatório");
        }
        
        if (serverUrl == null || serverUrl.trim().isEmpty()) {
            throw new IllegalStateException("URL do servidor é obrigatória");
        }
        
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalStateException("Nome de usuário é obrigatório");
        }
        
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalStateException("Senha é obrigatória");
        }
        
        if (timeout == null || timeout.isNegative()) {
            throw new IllegalStateException("Timeout deve ser positivo");
        }
        
        if (maxRetries < 0) {
            throw new IllegalStateException("Número de retries não pode ser negativo");
        }
        
        if (retryDelay == null || retryDelay.isNegative()) {
            throw new IllegalStateException("Delay de retry deve ser positivo");
        }
    }
    
    /**
     * Cria builder pré-configurado para Datasul.
     * 
     * @return Builder com configurações padrão Datasul
     */
    public static IntegrationConfigBuilder forDatasul() {
        return new IntegrationConfigBuilder()
            .withERPType(ERPType.DATASUL)
            .withTimeout(Duration.ofSeconds(60))
            .withRetryConfig(3, Duration.ofSeconds(10));
    }
    
    /**
     * Cria builder pré-configurado para Senior.
     * 
     * @return Builder com configurações padrão Senior
     */
    public static IntegrationConfigBuilder forSenior() {
        return new IntegrationConfigBuilder()
            .withERPType(ERPType.SENIOR)
            .withTimeout(Duration.ofSeconds(45))
            .withRetryConfig(2, Duration.ofSeconds(5));
    }
    
    /**
     * Cria builder pré-configurado para Protheus.
     * 
     * @return Builder com configurações padrão Protheus
     */
    public static IntegrationConfigBuilder forProtheus() {
        return new IntegrationConfigBuilder()
            .withERPType(ERPType.PROTHEUS)
            .withTimeout(Duration.ofSeconds(30))
            .withRetryConfig(3, Duration.ofSeconds(8));
    }
}

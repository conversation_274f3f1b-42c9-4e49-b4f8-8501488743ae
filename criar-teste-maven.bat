@echo off
echo Criando arquivo de teste para o projeto Maven...

set ARQUIVO=D:\Projetos\Java\integracaoDatasul-maven\src\test\java\com\infoaxis\TestWindchillMaven.java

(
echo package com.infoaxis.test;
echo.
echo import wt.util.WTException;
echo import wt.util.WTPropertyVetoException;
echo import wt.session.SessionHelper;
echo import wt.method.RemoteMethodServer;
echo import wt.pds.StatementSpec;
echo import wt.query.QuerySpec;
echo.
echo /**
echo  * Teste para verificar se as dependencias do Windchill
echo  * estao sendo reconhecidas no projeto Maven.
echo  */
echo public class TestWindchillMaven {
echo.
echo     public void testWindchillImports^(^) throws WTException {
echo         // Se este codigo compilar sem erros, as dependencias estao OK
echo         boolean isAuth = SessionHelper.manager.isAuthenticated^(^);
echo         RemoteMethodServer server = RemoteMethodServer.getDefault^(^);
echo         
echo         System.out.println^("✓ Windchill dependencies working in Maven!"^);
echo         System.out.println^("Authenticated: " + isAuth^);
echo     }
echo     
echo     public void testWindchillQuery^(^) throws WTException {
echo         QuerySpec qs = new QuerySpec^(^);
echo         StatementSpec stmt = new StatementSpec^(^);
echo         
echo         System.out.println^("✓ QuerySpec and StatementSpec accessible"^);
echo     }
echo     
echo     public static void main^(String[] args^) {
echo         TestWindchillMaven test = new TestWindchillMaven^(^);
echo         
echo         try {
echo             System.out.println^("=== Testando Dependencias Windchill no Maven ==="^);
echo             test.testWindchillImports^(^);
echo             test.testWindchillQuery^(^);
echo             System.out.println^("=== Todos os testes passaram! ==="^);
echo             
echo         } catch ^(Exception e^) {
echo             System.err.println^("✗ Teste falhou: " + e.getMessage^(^)^);
echo             e.printStackTrace^(^);
echo         }
echo     }
echo }
) > "%ARQUIVO%"

echo ✓ Arquivo de teste criado: %ARQUIVO%
echo.
echo Agora abra o VSCode no projeto Maven:
echo cd /d "D:\Projetos\Java\integracaoDatasul-maven"
echo code .
echo.
pause

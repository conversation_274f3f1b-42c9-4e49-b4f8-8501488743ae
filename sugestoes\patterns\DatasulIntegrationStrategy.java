package com.infoaxis.integracao.erp.datasul;

import com.infoaxis.integracao.core.service.ERPIntegrationStrategy;
import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.core.model.IntegrationRequest;
import com.infoaxis.integracao.core.model.IntegrationResponse;
import com.infoaxis.integracao.core.util.ErrorHandler;
import java.util.logging.Logger;

/**
 * Implementação específica para integração com Datasul/TOTVS.
 * Implementa o padrão Strategy para operações específicas do Datasul.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DatasulIntegrationStrategy implements ERPIntegrationStrategy {
    
    private static final Logger LOGGER = Logger.getLogger(DatasulIntegrationStrategy.class.getName());
    
    private final DatasulConnectionManager connectionManager;
    private final DatasulDataMapper dataMapper;
    
    /**
     * Construtor com injeção de dependências.
     * 
     * @param connectionManager Gerenciador de conexões Datasul
     * @param dataMapper Mapeador de dados específico
     */
    public DatasulIntegrationStrategy(DatasulConnectionManager connectionManager, 
                                    DatasulDataMapper dataMapper) {
        this.connectionManager = connectionManager;
        this.dataMapper = dataMapper;
    }
    
    @Override
    public ERPType getSupportedERPType() {
        return ERPType.DATASUL;
    }
    
    @Override
    public boolean validateRequest(IntegrationRequest request) throws ERPIntegrationException {
        if (request == null) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "datasul", 
                "Requisição não pode ser nula", null);
        }
        
        // Validações específicas do Datasul
        if (request.getCompanyCode() == null || request.getCompanyCode().trim().isEmpty()) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "datasul", 
                "Código da empresa é obrigatório para Datasul", null);
        }
        
        if (request.getOperation() == null) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "datasul", 
                "Operação não especificada", null);
        }
        
        // Validação específica de formato Datasul
        if (!isValidDatasulFormat(request)) {
            throw new ERPIntegrationException("VALIDATION_ERROR", "datasul", 
                "Formato de dados incompatível com Datasul", null);
        }
        
        return true;
    }
    
    @Override
    public IntegrationResponse executeIntegration(IntegrationRequest request) 
            throws ERPIntegrationException {
        
        LOGGER.info("Iniciando integração Datasul para operação: " + request.getOperation());
        
        try {
            // 1. Validar requisição
            validateRequest(request);
            
            // 2. Estabelecer conexão
            if (!connectionManager.isConnected()) {
                connectionManager.connect();
            }
            
            // 3. Mapear dados para formato Datasul
            Object datasulData = dataMapper.mapToDatasulFormat(request);
            
            // 4. Executar operação específica
            Object result = executeSpecificOperation(request.getOperation(), datasulData);
            
            // 5. Mapear resposta de volta
            IntegrationResponse response = dataMapper.mapFromDatasulFormat(result);
            
            LOGGER.info("Integração Datasul concluída com sucesso");
            return response;
            
        } catch (Exception e) {
            String errorMessage = ErrorHandler.handleIntegrationError("datasul", e);
            LOGGER.severe("Falha na integração Datasul: " + errorMessage);
            throw new ERPIntegrationException("DATASUL_ERROR", "datasul", errorMessage, e);
        }
    }
    
    @Override
    public boolean testConnection() throws ERPIntegrationException {
        try {
            return connectionManager.testConnection();
        } catch (Exception e) {
            throw ErrorHandler.createConnectionException("datasul", 
                connectionManager.getServerUrl(), 30, e);
        }
    }
    
    @Override
    public String[] getRequiredConfigurations() {
        return new String[]{
            "datasul.server.url",
            "datasul.database.name", 
            "datasul.username",
            "datasul.password",
            "datasul.company.code",
            "datasul.timeout.seconds"
        };
    }
    
    /**
     * Valida se os dados estão no formato esperado pelo Datasul.
     * 
     * @param request Requisição a ser validada
     * @return true se válida
     */
    private boolean isValidDatasulFormat(IntegrationRequest request) {
        // Implementar validações específicas do formato Datasul
        // Por exemplo: códigos de empresa, formatos de data, etc.
        return request.getCompanyCode().matches("\\d{2}"); // Exemplo: código com 2 dígitos
    }
    
    /**
     * Executa operação específica baseada no tipo.
     * 
     * @param operation Tipo de operação
     * @param data Dados formatados para Datasul
     * @return Resultado da operação
     * @throws ERPIntegrationException em caso de erro
     */
    private Object executeSpecificOperation(String operation, Object data) 
            throws ERPIntegrationException {
        
        switch (operation.toUpperCase()) {
            case "CREATE_ITEM":
                return connectionManager.createItem(data);
            case "UPDATE_ITEM":
                return connectionManager.updateItem(data);
            case "GET_ITEM":
                return connectionManager.getItem(data);
            case "DELETE_ITEM":
                return connectionManager.deleteItem(data);
            default:
                throw new ERPIntegrationException("UNSUPPORTED_OPERATION", "datasul", 
                    "Operação não suportada: " + operation, null);
        }
    }
}

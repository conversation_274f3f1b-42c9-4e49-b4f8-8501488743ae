# Solução: VSCode + Windchill - Reconhecimento de Dependências

## Problema Identificado
O VSCode não estava reconhecendo as dependências do Windchill (classes `wt.*`), resultando em:
- Imports sublinhados em vermelho
- Falta de autocomplete
- Erros de compilação falsos
- Navegação de código não funcionando

## Causa Raiz
O VSCode estava tentando interpretar o projeto como Maven, mas o projeto original é um projeto Eclipse com configurações específicas do Windchill definidas no arquivo `.classpath`.

## Solução Implementada

### 1. Configuração do VSCode (`.vscode/settings.json`)
```json
{
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.project.sourcePaths": [
        ".",
        "D:/Windchill/src"
    ],
    "java.project.outputPath": "D:/Windchill/codebase",
    "java.project.referencedLibraries": {
        "include": [
            "D:/Windchill/lib/*.jar",
            "D:/Windchill/codebase/WEB-INF/lib/*.jar",
            "D:/Windchill/srclib/**/*.jar",
            "D:/Windchill/thirdparty/**/*.jar"
        ]
    },
    "java.import.maven.enabled": false,
    "java.import.gradle.enabled": false
}
```

### 2. Configuração de Debug (`.vscode/launch.json`)
Adicionadas configurações específicas para debug com classpath do Windchill.

### 3. Scripts de Configuração
- `configurar-vscode-windchill.bat` - Script principal de configuração
- `reload-vscode-workspace.bat` - Script para recarregar workspace
- `fix-vscode-java-recognition.bat` - Script para correção avançada

### 4. Arquivo de Teste
- `TestWindchillDependencies.java` - Para verificar se as dependências estão funcionando

## Como Usar

### Primeira Configuração
1. Execute: `configurar-vscode-windchill.bat`
2. Siga as instruções exibidas
3. Feche completamente o VSCode
4. Reabra o VSCode na pasta do projeto
5. Aguarde o Java Language Server carregar

### Se Ainda Houver Problemas
1. No VSCode, pressione `Ctrl+Shift+P`
2. Execute: `Java: Reload Projects`
3. Execute: `Java: Rebuild Workspace`
4. Se necessário: `Developer: Reload Window`

### Verificação
1. Abra o arquivo `TestWindchillDependencies.java`
2. Os imports `wt.*` devem aparecer sem sublinhado vermelho
3. O autocomplete deve funcionar para classes do Windchill
4. A navegação (F12) deve funcionar

## Dependências Verificadas
✓ `D:/Windchill/lib/esi.jar`
✓ `D:/Windchill/lib/wnc.jar`
✓ `D:/Windchill/codebase/WEB-INF/lib/ieWeb.jar`

## Estrutura de Projeto
```
D:/eclipse/cust_Windchill_src/
├── .vscode/
│   ├── settings.json (configurações Java)
│   └── launch.json (configurações debug)
├── src/main/java/com/infoaxis/ (código fonte)
├── TestWindchillDependencies.java (arquivo de teste)
└── scripts de configuração (.bat)
```

## Notas Importantes
- O projeto mantém compatibilidade com Eclipse através dos arquivos `.classpath` e `.project`
- As configurações desabilitam Maven/Gradle para evitar conflitos
- O output é direcionado para `D:/Windchill/codebase` conforme necessário
- Cache do Java Language Server é limpo automaticamente pelos scripts

## Troubleshooting
Se os problemas persistirem:
1. Verifique se o Java está configurado corretamente
2. Confirme que todas as dependências do Windchill existem
3. Execute `fix-vscode-java-recognition.bat` para limpeza completa
4. Reinicie o VSCode completamente

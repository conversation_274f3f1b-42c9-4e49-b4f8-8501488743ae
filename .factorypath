<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<factorypath>
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/tool/AnnotationProcessing.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/tool/Annotations.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/wnc/CommonCore.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/jmxcore/WtLogR.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/install/InstallUtil.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/commons-collections4.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/log4j-api.jar' enabled='true' runInBatchMode='false' />
  <factorypathentry kind='EXTJAR' id='D:/Windchill/srclib/log4j-core.jar' enabled='true' runInBatchMode='false' />
</factorypath>
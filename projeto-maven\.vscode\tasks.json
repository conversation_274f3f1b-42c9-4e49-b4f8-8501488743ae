{"version": "2.0.0", "tasks": [{"label": "Maven: Clean", "type": "shell", "command": "mvn", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Maven: <PERSON><PERSON><PERSON>", "type": "shell", "command": "mvn", "args": ["compile"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$maven-compiler-java"], "dependsOn": "Maven: Clean"}, {"label": "Maven: Test", "type": "shell", "command": "mvn", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$maven-compiler-java"]}, {"label": "Maven: Package", "type": "shell", "command": "mvn", "args": ["package"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$maven-compiler-java"]}, {"label": "Build and Deploy to Windchill", "type": "shell", "command": "scripts/build.bat", "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": ["$maven-compiler-java"], "detail": "Compila o projeto e deploya no codebase do Windchill"}, {"label": "Deploy to Windchill", "type": "shell", "command": "scripts/deploy.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "detail": "Faz deploy completo com backup para o Windchill"}, {"label": "Run Tests with Coverage", "type": "shell", "command": "mvn", "args": ["clean", "test", "jacoco:report"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$maven-compiler-java"], "detail": "Executa testes com relatório de cobertura"}, {"label": "Generate Documentation", "type": "shell", "command": "mvn", "args": ["javadoc:javadoc"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Gera documentação JavaDoc"}, {"label": "Validate Project", "type": "shell", "command": "mvn", "args": ["validate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Valida configuração do projeto Maven"}, {"label": "Clean Windchill Classes", "type": "shell", "command": "rmdir", "args": ["/s", "/q", "D:\\Windchill\\codebase\\com\\infoaxis"], "options": {"shell": {"executable": "cmd.exe", "args": ["/c"]}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Remove classes compiladas do codebase do Windchill"}]}
{
    // Configurações Java
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.dependency.packagePresentation": "hierarchical",
    "java.dependency.syncWithFolderExplorer": true,
    "java.debug.settings.onBuildFailureProceed": true,
    "java.debug.settings.showQualifiedNames": false,
    "java.format.settings.url": ".vscode/java-formatter.xml",
    
    // Configurações Maven
    "maven.executable.path": "mvn",
    "maven.terminal.useJavaHome": true,
    "maven.view": "hierarchical",
    
    // Source paths para o projeto
    "java.project.sourcePaths": [
        "src/main/java",
        "src/test/java"
    ],
    
    // Bibliotecas referenciadas (Windchill)
    "java.project.referencedLibraries": [
        "D:/Windchill/lib/**/*.jar",
        "D:/Windchill/codebase/WEB-INF/lib/**/*.jar",
        "D:/Windchill/srclib/**/*.jar"
    ],
    
    // Configurações JVM
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms1G -Xlog:disable",
    
    // Configurações de encoding
    "files.encoding": "utf8",
    "java.project.encoding": "UTF-8",
    
    // Configurações de formatação
    "java.format.settings.profile": "InfoAxis",
    "java.format.onType.enabled": true,
    "java.format.onSave.enabled": true,
    
    // Configurações de teste
    "java.test.config": {
        "name": "windchill-erp-tests",
        "workingDirectory": "${workspaceFolder}",
        "vmArgs": [
            "-Dfile.encoding=UTF-8",
            "-Djava.awt.headless=true"
        ]
    },
    
    // Configurações de debug
    "java.debug.logLevel": "warn",
    "java.debug.settings.enableRunDebugCodeLens": true,
    "java.debug.settings.forceBuildBeforeLaunch": true,
    
    // Configurações do editor
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true,
        "source.fixAll": true
    },
    
    // Configurações de arquivos
    "files.associations": {
        "*.java": "java",
        "*.xml": "xml",
        "*.properties": "properties"
    },
    
    // Configurações de busca
    "search.exclude": {
        "**/target": true,
        "**/node_modules": true,
        "**/.git": true,
        "**/logs": true
    },
    
    // Configurações de terminal
    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    "terminal.integrated.cwd": "${workspaceFolder}",
    
    // Configurações específicas do Windchill
    "java.project.outputPath": "D:/Windchill/codebase",
    
    // Configurações de extensões
    "extensions.ignoreRecommendations": false,
    
    // Configurações de Git
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    
    // Configurações de workspace
    "workbench.startupEditor": "readme",
    "workbench.editor.enablePreview": false,
    
    // Configurações de problemas
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    
    // Configurações de IntelliSense
    "java.completion.enabled": true,
    "java.completion.guessMethodArguments": true,
    "java.completion.favoriteStaticMembers": [
        "org.junit.jupiter.api.Assertions.*",
        "org.mockito.Mockito.*",
        "java.util.Objects.requireNonNull",
        "java.util.Objects.isNull",
        "java.util.Objects.nonNull"
    ],
    
    // Configurações de refactoring
    "java.refactor.renameFromFileExplorer": "autoApply",
    
    // Configurações de documentação
    "java.help.firstView": "gettingStarted",
    
    // Configurações de performance
    "java.maxConcurrentBuilds": 2,
    "java.import.gradle.offline.enabled": false
}

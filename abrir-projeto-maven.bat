@echo off
echo ========================================
echo  Abrindo Projeto Maven (Nova Instancia)
echo ========================================
echo.

set PROJETO=D:\Projetos\Java\integracaoDatasul-maven

echo Verificando se projeto existe...
if not exist "%PROJETO%" (
    echo ERRO: Projeto nao encontrado em %PROJETO%
    pause
    exit /b 1
)

echo Projeto encontrado: %PROJETO%
echo.

echo Abrindo NOVA instancia do VSCode...
echo (Mantem a instancia atual aberta)
echo.

REM Abre nova instancia sem fechar a atual
start "" code --new-window "%PROJETO%"

echo.
echo ========================================
echo  Nova Instancia Aberta!
echo ========================================
echo.
echo Uma NOVA janela do VSCode foi aberta com o projeto Maven.
echo Sua instancia atual permanece aberta.
echo.
echo Na NOVA janela, aguarde 1-2 minutos para processar.
echo.
echo Voce deve ver na NOVA janela:
echo [✓] "Java Projects" na barra lateral esquerda
echo [✓] Icone do Maven na barra lateral
echo [✓] Imports clicaveis (em azul)
echo [✓] Autocomplete funcionando
echo.
echo Para testar na NOVA janela:
echo 1. Abra: src\main\java\com\infoaxis\integracaoDatasul\esi\CommERPHelper.java
echo 2. Clique em algum import para ver se navega
echo 3. Digite "System." para ver autocomplete
echo.
echo Se nao funcionar na NOVA janela:
echo 1. Pressione Ctrl+Shift+P (na nova janela)
echo 2. Digite: "Java: Reload Projects"
echo 3. Aguarde processar
echo.

pause

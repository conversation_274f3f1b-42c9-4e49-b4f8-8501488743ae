@echo off
echo Criando estrutura do projeto Maven...

REM Criar diretórios principais
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\core\exception" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\core\util" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\core\service" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\core\factory" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\core\model" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\erp\datasul" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\erp\senior" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\java\com\infoaxis\integracao\erp\protheus" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\main\resources" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\test\java" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\src\test\resources" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\.vscode" 2>nul
mkdir "D:\Projetos\Java\windchill-erp-integration\scripts" 2>nul

echo Estrutura criada com sucesso!
echo.
echo Agora você pode:
echo 1. Abrir o VSCode no diretório: code "D:\Projetos\Java\windchill-erp-integration"
echo 2. Copiar seus arquivos Java existentes para a nova estrutura
echo 3. Executar o primeiro build com Maven
echo.
pause

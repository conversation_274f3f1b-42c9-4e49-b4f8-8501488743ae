@echo off
echo ========================================
echo  Teste Final - Projeto integracaoDatasul-maven
echo ========================================

set PROJECT_PATH=D:\Projetos\Java\integracaoDatasul-maven

echo.
echo [1/4] Verificando projeto...
cd /d "%PROJECT_PATH%"
if %errorlevel% neq 0 (
    echo ERRO: Projeto nao encontrado em %PROJECT_PATH%
    pause
    exit /b 1
)

echo ✓ Projeto encontrado: %PROJECT_PATH%

echo.
echo [2/4] Verificando configuracoes aplicadas...
if exist ".vscode\settings.json" (
    findstr "6G" .vscode\settings.json >nul
    if %errorlevel%==0 (
        echo ✓ Memoria JVM configurada para 6GB
    ) else (
        echo ⚠ Configuracao de memoria pode nao ter sido aplicada
    )
) else (
    echo ✗ Arquivo settings.json nao encontrado
)

if exist "java_heap_dumps" (
    echo ✓ Diretorio heap_dumps criado
) else (
    echo ⚠ Diretorio heap_dumps nao encontrado
)

echo.
echo [3/4] Verificando dependencias Windchill...
set WINDCHILL_HOME=D:\Windchill
if exist "%WINDCHILL_HOME%\lib\esi.jar" (
    echo ✓ esi.jar encontrado
) else (
    echo ✗ esi.jar NAO encontrado em %WINDCHILL_HOME%\lib\
)

if exist "%WINDCHILL_HOME%\codebase\WEB-INF\lib\ieWeb.jar" (
    echo ✓ ieWeb.jar encontrado
) else (
    echo ✗ ieWeb.jar NAO encontrado
)

echo.
echo [4/4] Verificando arquivos de codigo...
if exist "src\main\java\com\infoaxis\integracaoDatasul\esi\CommERPHelper.java" (
    echo ✓ CommERPHelper.java encontrado
) else (
    echo ✗ CommERPHelper.java NAO encontrado
)

echo.
echo ========================================
echo  RESUMO DAS CORRECOES APLICADAS
echo ========================================
echo.
echo ✓ Arquivos de crash removidos
echo ✓ Cache VSCode limpo
echo ✓ Memoria JVM aumentada: 2G → 6G
echo ✓ Memoria inicial: 100m → 1G
echo ✓ Heap dumps habilitados
echo ✓ Garbage Collector otimizado
echo ✓ Dependencias Windchill configuradas
echo.
echo ⚠ PROBLEMA IDENTIFICADO:
echo   - Bibliotecas InfoAxis (com.infoaxis.core.*) nao estao
echo     definidas como dependencias no pom.xml
echo   - Isso pode causar erros de import no VSCode
echo.
echo ========================================
echo  PROXIMOS PASSOS RECOMENDADOS
echo ========================================
echo.
echo 1. FECHE o VSCode completamente
echo 2. Aguarde 10 segundos
echo 3. Abra o VSCode NESTE diretorio:
echo    %PROJECT_PATH%
echo 4. Aguarde a indexacao (10-15 minutos)
echo 5. Verifique se imports wt.* funcionam
echo 6. Se imports com.infoaxis.core.* derem erro:
echo    - Adicione as bibliotecas InfoAxis ao pom.xml
echo    - Ou configure como system dependencies
echo.
echo Pressione qualquer tecla para continuar...
pause >nul

echo.
echo Abrindo VSCode no projeto...
start "" "code" "%PROJECT_PATH%"

echo.
echo ✓ VSCode aberto no projeto correto!
echo Aguarde a indexacao e teste os imports.
echo.

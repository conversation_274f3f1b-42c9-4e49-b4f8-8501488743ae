package com.infoaxis.integracao.core.service;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.core.model.IntegrationRequest;
import com.infoaxis.integracao.core.model.IntegrationResponse;

/**
 * Interface Strategy para implementações específicas de cada ERP.
 * Define contrato comum para todas as integrações, facilitando a adição
 * de novos ERPs sem modificar código existente.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ERPIntegrationStrategy {
    
    /**
     * Retorna o tipo de ERP suportado por esta estratégia.
     * 
     * @return Tipo do ERP (DATASUL, SENIOR, PROTHEUS, etc.)
     */
    ERPType getSupportedERPType();
    
    /**
     * Valida se a requisição é compatível com este ERP.
     * Implementa validações específicas de cada sistema.
     * 
     * @param request Requisição de integração
     * @return true se válida
     * @throws ERPIntegrationException se inválida
     */
    boolean validateRequest(IntegrationRequest request) throws ERPIntegrationException;
    
    /**
     * Executa a integração específica do ERP.
     * Método principal que implementa a lógica de integração.
     * 
     * @param request Dados da requisição
     * @return Resposta da integração
     * @throws ERPIntegrationException em caso de erro
     */
    IntegrationResponse executeIntegration(IntegrationRequest request) throws ERPIntegrationException;
    
    /**
     * Testa conectividade com o ERP.
     * Útil para health checks e diagnósticos.
     * 
     * @return true se conectado
     * @throws ERPIntegrationException em caso de falha
     */
    boolean testConnection() throws ERPIntegrationException;
    
    /**
     * Retorna configurações específicas necessárias para este ERP.
     * 
     * @return Array com nomes das configurações obrigatórias
     */
    String[] getRequiredConfigurations();
    
    /**
     * Retorna versão da API suportada pelo ERP.
     * 
     * @return Versão da API (ex: "1.0", "2.1")
     */
    default String getSupportedApiVersion() {
        return "1.0";
    }
    
    /**
     * Indica se este ERP suporta transações.
     * 
     * @return true se suporta transações
     */
    default boolean supportsTransactions() {
        return false;
    }
    
    /**
     * Enum para tipos de ERP suportados.
     * Facilita adição de novos ERPs de forma controlada.
     */
    enum ERPType {
        DATASUL("Datasul/TOTVS", "datasul"),
        SENIOR("Senior", "senior"),
        PROTHEUS("Protheus", "protheus"),
        SAP("SAP", "sap"),
        ORACLE("Oracle ERP", "oracle");
        
        private final String displayName;
        private final String configPrefix;
        
        ERPType(String displayName, String configPrefix) {
            this.displayName = displayName;
            this.configPrefix = configPrefix;
        }
        
        /**
         * Nome amigável para exibição.
         * 
         * @return Nome para exibição
         */
        public String getDisplayName() {
            return displayName;
        }
        
        /**
         * Prefixo usado nas configurações.
         * 
         * @return Prefixo de configuração
         */
        public String getConfigPrefix() {
            return configPrefix;
        }
        
        /**
         * Busca tipo de ERP por nome.
         * 
         * @param name Nome do ERP
         * @return Tipo encontrado ou null
         */
        public static ERPType fromName(String name) {
            if (name == null) return null;
            
            for (ERPType type : values()) {
                if (type.name().equalsIgnoreCase(name) || 
                    type.displayName.equalsIgnoreCase(name) ||
                    type.configPrefix.equalsIgnoreCase(name)) {
                    return type;
                }
            }
            return null;
        }
    }
}

@echo off
REM ============================================================================
REM Script de Deploy para Windchill ERP Integration
REM ============================================================================

echo.
echo ========================================
echo  Windchill ERP Integration - Deploy
echo ========================================
echo.

REM Verificar se o Windchill está rodando
echo Verificando status do Windchill...
tasklist /FI "IMAGENAME eq java.exe" | find "java.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo.
    echo AVISO: O Windchill parece estar rodando!
    echo É recomendado parar o servidor antes do deploy.
    echo.
    echo Deseja continuar mesmo assim? (S/N)
    set /p continue=
    if /i not "%continue%"=="S" (
        echo Deploy cancelado pelo usuário.
        pause
        exit /b 1
    )
)

REM Navegar para o diretório do projeto
cd /d "%~dp0.."

echo.
echo [1/4] Fazendo backup das classes atuais...
set BACKUP_DIR=D:\Windchill\backup\%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir "%BACKUP_DIR%" 2>nul

if exist "D:\Windchill\codebase\com\infoaxis" (
    xcopy "D:\Windchill\codebase\com\infoaxis" "%BACKUP_DIR%\infoaxis\" /E /I /Q
    echo Backup criado em: %BACKUP_DIR%
) else (
    echo Nenhuma classe anterior encontrada para backup.
)

echo.
echo [2/4] Limpando classes antigas...
call mvn clean
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha na limpeza!
    pause
    exit /b 1
)

echo.
echo [3/4] Compilando e deployando...
call mvn compile
if %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha na compilação!
    echo.
    echo Restaurando backup...
    if exist "%BACKUP_DIR%\infoaxis" (
        xcopy "%BACKUP_DIR%\infoaxis" "D:\Windchill\codebase\com\infoaxis\" /E /I /Q
        echo Backup restaurado com sucesso.
    )
    pause
    exit /b 1
)

echo.
echo [4/4] Verificando deploy...
if exist "D:\Windchill\codebase\com\infoaxis" (
    echo Classes deployadas com sucesso!
    dir "D:\Windchill\codebase\com\infoaxis" /S /B | find ".class" | wc -l > temp_count.txt
    set /p class_count=<temp_count.txt
    del temp_count.txt
    echo Total de classes deployadas: %class_count%
) else (
    echo ERRO: Classes não foram deployadas corretamente!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  DEPLOY CONCLUÍDO COM SUCESSO!
echo ========================================
echo.
echo Backup disponível em: %BACKUP_DIR%
echo Classes deployadas em: D:\Windchill\codebase\com\infoaxis
echo.
echo PRÓXIMOS PASSOS:
echo 1. Reinicie o servidor Windchill para aplicar as mudanças
echo 2. Teste as funcionalidades modificadas
echo 3. Monitore os logs para verificar se não há erros
echo.

pause

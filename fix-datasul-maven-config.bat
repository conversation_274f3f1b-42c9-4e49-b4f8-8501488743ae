@echo off
echo ========================================
echo  Corrigindo Configuracoes VSCode
echo  Projeto: integracaoDatasul-maven
echo ========================================

set PROJECT_PATH=D:\Projetos\Java\integracaoDatasul-maven

echo.
echo [1/5] Navegando para o projeto...
cd /d "%PROJECT_PATH%"
if %errorlevel% neq 0 (
    echo ERRO: Nao foi possivel acessar %PROJECT_PATH%
    pause
    exit /b 1
)

echo.
echo [2/5] Fazendo backup das configuracoes atuais...
if exist ".vscode\settings.json" (
    copy ".vscode\settings.json" ".vscode\settings-backup.json" >nul
    echo Backup criado: settings-backup.json
) else (
    echo Arquivo settings.json nao encontrado
)

echo.
echo [3/5] Limpando arquivos de crash restantes...
del /q hs_err_pid*.log 2>nul
del /q hs_err_pid*.mdmp 2>nul
del /q replay_pid*.log 2>nul
echo Arquivos de crash removidos

echo.
echo [4/5] Criando diretorio para heap dumps...
if not exist "java_heap_dumps" mkdir java_heap_dumps
echo Diretorio criado: java_heap_dumps

echo.
echo [5/5] Atualizando configuracoes de memoria JVM...
powershell -Command "(Get-Content '.vscode\settings.json') -replace '\"java.jdt.ls.vmargs\": \".*\"', '\"java.jdt.ls.vmargs\": \"-XX:+UseParallelGC -Xmx6G -Xms1G -Dsun.zip.disableMemoryMapping=true -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./java_heap_dumps -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90\"' | Set-Content '.vscode\settings.json'"

echo.
echo ✓ Configuracoes atualizadas!
echo.
echo MUDANCAS APLICADAS:
echo - Memoria JVM aumentada: 2G → 6G
echo - Memoria inicial: 100m → 1G  
echo - Heap dumps habilitados
echo - Garbage Collector otimizado
echo - Arquivos de crash removidos
echo.
echo PROXIMOS PASSOS:
echo 1. Feche o VSCode completamente
echo 2. Aguarde 10 segundos
echo 3. Reabra o VSCode neste projeto
echo 4. Aguarde a indexacao (pode levar 10-15 minutos)
echo 5. Teste imports do Windchill
echo.
pause

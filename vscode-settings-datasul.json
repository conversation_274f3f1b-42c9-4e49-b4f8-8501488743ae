{
    // Configurações Java
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.dependency.packagePresentation": "hierarchical",
    "java.dependency.syncWithFolderExplorer": true,
    
    // Configurações Maven
    "maven.executable.path": "mvn",
    "maven.terminal.useJavaHome": true,
    "maven.view": "hierarchical",
    
    // Source paths para o projeto
    "java.project.sourcePaths": [
        "src/main/java",
        "src/test/java"
    ],
    
    // Bibliotecas referenciadas (suas dependências)
    "java.project.referencedLibraries": [
        "D:/Windchill/lib/**/*.jar",
        "D:/Windchill/codebase/WEB-INF/lib/**/*.jar",
        "D:/eclipse/cust_Windchill_src/**/*.jar",
        "D:/Windchill/src/com/infoaxis/core/**/*.java",
        "D:/Windchill/src/com/infoaxis/esi/**/*.java"
    ],
    
    // Configurações de encoding
    "files.encoding": "utf8",
    "java.project.encoding": "UTF-8",
    
    // Configurações de formatação
    "java.format.onType.enabled": true,
    "java.format.onSave.enabled": true,
    
    // Configurações do editor
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true,
        "source.fixAll": true
    },
    
    // Configurações de arquivos
    "files.associations": {
        "*.java": "java",
        "*.xml": "xml",
        "*.properties": "properties"
    },
    
    // Configurações de busca
    "search.exclude": {
        "**/target": true,
        "**/node_modules": true,
        "**/.git": true,
        "**/logs": true,
        "**/_gen": true
    },
    
    // Configurações específicas do Windchill
    "java.project.outputPath": "D:/Windchill/codebase",
    
    // Configurações de IntelliSense
    "java.completion.enabled": true,
    "java.completion.guessMethodArguments": true,
    "java.completion.favoriteStaticMembers": [
        "java.util.Objects.requireNonNull",
        "java.util.Objects.isNull",
        "java.util.Objects.nonNull"
    ],
    
    // Configurações de performance
    "java.maxConcurrentBuilds": 2,
    
    // Configurações de terminal
    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    "terminal.integrated.cwd": "${workspaceFolder}"
}

package com.infoaxis.integracao.core.service;

import com.infoaxis.integracao.core.exception.ERPIntegrationException;
import com.infoaxis.integracao.core.model.IntegrationRequest;
import com.infoaxis.integracao.core.model.IntegrationResponse;

/**
 * Interface Strategy para implementações específicas de cada ERP.
 * Define contrato comum para todas as integrações.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ERPIntegrationStrategy {
    
    /**
     * Retorna o tipo de ERP suportado por esta estratégia.
     * 
     * @return Tipo do ERP (DATASUL, SENIOR, PROTHEUS, etc.)
     */
    ERPType getSupportedERPType();
    
    /**
     * Valida se a requisição é compatível com este ERP.
     * 
     * @param request Requisição de integração
     * @return true se válida
     * @throws ERPIntegrationException se inválida
     */
    boolean validateRequest(IntegrationRequest request) throws ERPIntegrationException;
    
    /**
     * Executa a integração específica do ERP.
     * 
     * @param request Dados da requisição
     * @return Resposta da integração
     * @throws ERPIntegrationException em caso de erro
     */
    IntegrationResponse executeIntegration(IntegrationRequest request) throws ERPIntegrationException;
    
    /**
     * Testa conectividade com o ERP.
     * 
     * @return true se conectado
     * @throws ERPIntegrationException em caso de falha
     */
    boolean testConnection() throws ERPIntegrationException;
    
    /**
     * Retorna configurações específicas necessárias para este ERP.
     * 
     * @return Array com nomes das configurações obrigatórias
     */
    String[] getRequiredConfigurations();
    
    /**
     * Enum para tipos de ERP suportados.
     */
    enum ERPType {
        DATASUL("Datasul/TOTVS"),
        SENIOR("Senior"),
        PROTHEUS("Protheus"),
        SAP("SAP"),
        ORACLE("Oracle ERP");
        
        private final String displayName;
        
        ERPType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
